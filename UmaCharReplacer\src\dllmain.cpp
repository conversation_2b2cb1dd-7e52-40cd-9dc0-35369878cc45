#include "common.hpp"
#include "config.hpp"
#include <filesystem>
#include <chrono>

// Forward declarations
extern void attach();
extern void detach();

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Disable thread library calls for performance
        DisableThreadLibraryCalls(hModule);

        // When loaded by <PERSON><PERSON><PERSON>, we need to delay initialization
        // until the game is fully ready
        std::thread([]() {
            // Wait a bit for <PERSON><PERSON><PERSON> to finish its initialization
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            attach();
        }).detach();
        break;

    case DLL_PROCESS_DETACH:
        detach();
        break;

    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }

    return TRUE;
}
