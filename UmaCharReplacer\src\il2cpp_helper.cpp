#include "il2cpp_helper.hpp"

// Global IL2CPP function pointers
il2cpp_field_get_offset_t il2cpp_field_get_offset = nullptr;
il2cpp_class_get_field_from_name_t il2cpp_class_get_field_from_name = nullptr;
il2cpp_domain_get_t il2cpp_domain_get = nullptr;
il2cpp_domain_assembly_open_t il2cpp_domain_assembly_open = nullptr;
il2cpp_assembly_get_image_t il2cpp_assembly_get_image = nullptr;
il2cpp_class_from_name_t il2cpp_class_from_name = nullptr;

namespace il2cpp_helper {
    CharacterFields g_character_fields;

    // IL2CPP domain pointer (cached)
    void* il2cpp_domain = nullptr;

    // GameAssembly handle (set by hook detection)
    HMODULE game_assembly_handle = nullptr;

    // Macro for resolving IL2CPP imports (following Hachimi pattern)
    #define RESOLVE_IMPORT(name) name = reinterpret_cast<name##_t>(GetProcAddress(game_assembly_handle, #name))

    void set_game_assembly_handle(HMODULE handle) {
        game_assembly_handle = handle;
        log_message("GameAssembly handle set for IL2CPP resolution");
    }

    bool initialize() {
        log_message("Initializing IL2CPP helper");

        // Check if GameAssembly handle was set by LoadLibraryW hook
        if (!game_assembly_handle) {
            // Fallback: try to get GameAssembly.dll module directly
            game_assembly_handle = GetModuleHandleW(L"GameAssembly.dll");
            if (!game_assembly_handle) {
                log_message("GameAssembly.dll not found - IL2CPP functions unavailable");
                log_message("This is expected if the game hasn't loaded GameAssembly yet");
                return false;
            }
            log_message("GameAssembly.dll found via GetModuleHandle (fallback)");
        }

        log_message("GameAssembly.dll available, resolving IL2CPP function pointers");

        // First, let's check what modules are available
        log_message("Checking available modules for IL2CPP functions:");

        // Check GameAssembly.dll
        auto gameassembly = GetModuleHandleW(L"GameAssembly.dll");
        log_message("  GameAssembly.dll: " + std::string(gameassembly ? "FOUND" : "NOT FOUND"));

        // Check UnityPlayer.dll (sometimes IL2CPP functions are here)
        auto unityplayer = GetModuleHandleW(L"UnityPlayer.dll");
        log_message("  UnityPlayer.dll: " + std::string(unityplayer ? "FOUND" : "NOT FOUND"));

        // Check if IL2CPP functions are in UnityPlayer instead
        if (unityplayer) {
            log_message("Trying IL2CPP function resolution in UnityPlayer.dll");
            auto test_domain_get = GetProcAddress(unityplayer, "il2cpp_domain_get");
            if (test_domain_get) {
                log_message("IL2CPP functions found in UnityPlayer.dll - switching to UnityPlayer");
                game_assembly_handle = unityplayer;
            }
        }

        // Resolve IL2CPP function pointers using Hachimi pattern
        RESOLVE_IMPORT(il2cpp_domain_get);
        RESOLVE_IMPORT(il2cpp_domain_assembly_open);
        RESOLVE_IMPORT(il2cpp_assembly_get_image);
        RESOLVE_IMPORT(il2cpp_class_from_name);
        RESOLVE_IMPORT(il2cpp_class_get_field_from_name);
        RESOLVE_IMPORT(il2cpp_field_get_offset);

        // Log which functions were found/missing
        log_message("IL2CPP function resolution results:");
        log_message("  il2cpp_domain_get: " + std::string(il2cpp_domain_get ? "FOUND" : "MISSING"));
        log_message("  il2cpp_domain_assembly_open: " + std::string(il2cpp_domain_assembly_open ? "FOUND" : "MISSING"));
        log_message("  il2cpp_assembly_get_image: " + std::string(il2cpp_assembly_get_image ? "FOUND" : "MISSING"));
        log_message("  il2cpp_class_from_name: " + std::string(il2cpp_class_from_name ? "FOUND" : "MISSING"));
        log_message("  il2cpp_class_get_field_from_name: " + std::string(il2cpp_class_get_field_from_name ? "FOUND" : "MISSING"));
        log_message("  il2cpp_field_get_offset: " + std::string(il2cpp_field_get_offset ? "FOUND" : "MISSING"));

        // Check if all required functions were resolved
        if (!il2cpp_domain_get || !il2cpp_domain_assembly_open ||
            !il2cpp_assembly_get_image || !il2cpp_class_from_name ||
            !il2cpp_class_get_field_from_name || !il2cpp_field_get_offset) {
            log_message("Failed to resolve some IL2CPP function pointers");
            log_message("This may indicate an incompatible game version or missing IL2CPP exports");

            // Try some alternative function names that might exist
            log_message("Trying alternative IL2CPP function names:");
            auto alt_domain = GetProcAddress(game_assembly_handle, "il2cpp_get_domain");
            log_message("  il2cpp_get_domain: " + std::string(alt_domain ? "FOUND" : "MISSING"));

            auto alt_assembly = GetProcAddress(game_assembly_handle, "il2cpp_assembly_open");
            log_message("  il2cpp_assembly_open: " + std::string(alt_assembly ? "FOUND" : "MISSING"));

            // Check if this is a newer Unity version with different exports
            auto unity_version = GetProcAddress(game_assembly_handle, "GetUnityVersion");
            log_message("  GetUnityVersion: " + std::string(unity_version ? "FOUND" : "MISSING"));

            log_message("IL2CPP functions not available - switching to direct memory approach");
            log_message("This is normal for some Unity versions - character replacement will use alternative methods");

            // For now, we'll continue without IL2CPP functions and use direct hooking
            // This matches how some versions of Trainers-Legend-G work
            return true; // Return true to allow character replacer to try direct hooking
        }

        log_message("IL2CPP function pointers resolved successfully");

        // Get and cache the IL2CPP domain
        il2cpp_domain = il2cpp_domain_get();
        if (!il2cpp_domain) {
            log_message("Failed to get IL2CPP domain");
            return false;
        }

        log_message("IL2CPP domain obtained successfully");

        // Initialize character fields
        if (!initialize_character_fields()) {
            log_message("Failed to initialize character fields");
            return false;
        }

        return true;
    }

    void* get_class(const char* assembly_name, const char* namespace_name, const char* class_name) {
        if (!il2cpp_domain || !il2cpp_domain_assembly_open ||
            !il2cpp_assembly_get_image || !il2cpp_class_from_name) {
            log_message("IL2CPP functions not initialized for get_class");
            return nullptr;
        }

        auto assembly = il2cpp_domain_assembly_open(il2cpp_domain, assembly_name);
        if (!assembly) {
            log_message("Failed to open assembly: " + std::string(assembly_name));
            return nullptr;
        }

        auto image = il2cpp_assembly_get_image(assembly);
        if (!image) {
            log_message("Failed to get image from assembly: " + std::string(assembly_name));
            return nullptr;
        }

        auto klass = il2cpp_class_from_name(image, namespace_name, class_name);
        if (!klass) {
            log_message("Failed to find class: " + std::string(namespace_name) + "::" + std::string(class_name));
            return nullptr;
        }

        log_message("Successfully found class: " + std::string(namespace_name) + "::" + std::string(class_name));
        return klass;
    }

    FieldInfo* get_field(void* klass, const char* field_name) {
        if (!klass || !il2cpp_class_get_field_from_name) {
            log_message("Invalid parameters for get_field");
            return nullptr;
        }

        auto field = il2cpp_class_get_field_from_name(klass, field_name);
        if (!field) {
            log_message("Failed to find field: " + std::string(field_name));
            return nullptr;
        }

        log_message("Successfully found field: " + std::string(field_name));
        return field;
    }

    size_t get_field_offset(FieldInfo* field) {
        if (!field) {
            return 0;
        }

        // For FieldInfo, we can use the offset directly or call il2cpp_field_get_offset
        if (il2cpp_field_get_offset) {
            return il2cpp_field_get_offset(field);
        } else {
            // Fallback: use the offset field directly
            return field->offset;
        }
    }

    bool initialize_character_fields() {
        log_message("Initializing character fields");

        // Get CharacterBuildInfo class
        auto character_build_info_class = get_class("Assembly-CSharp", "Gallop", "CharacterBuildInfo");
        if (!character_build_info_class) {
            log_message("Failed to get CharacterBuildInfo class");
            return false;
        }

        // Get field pointers
        g_character_fields.charaId = get_field(character_build_info_class, "CharaId");
        g_character_fields.dressId = get_field(character_build_info_class, "DressId");
        g_character_fields.headModelSubId = get_field(character_build_info_class, "HeadModelSubId");
        g_character_fields.motionDressId = get_field(character_build_info_class, "MotionDressId");
        g_character_fields.cardId = get_field(character_build_info_class, "CardId");

        if (!g_character_fields.charaId || !g_character_fields.dressId || 
            !g_character_fields.headModelSubId || !g_character_fields.motionDressId || 
            !g_character_fields.cardId) {
            log_message("Failed to get some character fields");
            return false;
        }

        log_message("Character fields initialized successfully");
        return true;
    }
}
