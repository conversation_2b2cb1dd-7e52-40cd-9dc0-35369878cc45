#include "common.hpp"
#include "config.hpp"
#include "character_replacer.hpp"
#include "il2cpp_helper.hpp"
#include <MinHook.h>

// Global variables
bool g_enable_home_char_replace = false;
bool g_enable_global_char_replace = false;
bool g_global_char_replace_Universal = true;
bool g_enable_console = false;
bool g_enable_logging = true;
bool g_hook_ready = false;

CharacterReplacementMap g_home_char_replace;
CharacterReplacementMap g_global_char_replace;
CharacterReplacementMap g_global_mini_char_replace;

// Hook variables
static bool mh_initialized = false;

// Utility functions
void create_debug_console() {
    if (!g_enable_console) return;

    AllocConsole();
    FILE* pCout;
    freopen_s(&pCout, "CONOUT$", "w", stdout);
    FILE* pCerr;
    freopen_s(&pCerr, "CONOUT$", "w", stderr);
    FILE* pCin;
    freopen_s(&pCin, "CONIN$", "r", stdin);

    SetConsoleTitleW(L"UmaCharReplacer Debug Console");
    log_message("Debug console initialized");
}

void log_message(const std::string& message) {
    if (!g_enable_logging) return;

    auto timestamp = get_current_time_string();
    auto full_message = "[" + timestamp + "] " + message;
    
    if (g_enable_console) {
        std::cout << full_message << std::endl;
    }
    
    // Also write to log file in hachimi directory
    std::ofstream log_file("hachimi/UmaCharReplacer.log", std::ios::app);
    if (log_file.is_open()) {
        log_file << full_message << std::endl;
        log_file.close();
    }
}

std::string get_current_time_string() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// Hook functions (simplified for Hachimi mode)
namespace hooks {
    // No LoadLibraryW hook needed when loaded by Hachimi
}

// Main hook initialization (when loaded by Hachimi)
bool init_hook() {
    if (mh_initialized) {
        return false;
    }

    if (MH_Initialize() != MH_OK) {
        log_message("Failed to initialize MinHook");
        return false;
    }

    mh_initialized = true;
    log_message("MinHook initialized successfully");

    // When loaded by Hachimi, GameAssembly and cri_ware_unity should already be loaded
    log_message("Checking for already loaded game modules (Hachimi mode)");

    auto game_assembly = GetModuleHandleW(L"GameAssembly.dll");
    auto cri_ware = GetModuleHandleW(L"cri_ware_unity.dll");

    log_message("GameAssembly.dll: " + std::string(game_assembly ? "LOADED" : "NOT LOADED"));
    log_message("cri_ware_unity.dll: " + std::string(cri_ware ? "LOADED" : "NOT LOADED"));

    if (game_assembly) {
        log_message("GameAssembly.dll found - setting IL2CPP handle");
        il2cpp_helper::set_game_assembly_handle(game_assembly);
    }

    // Initialize immediately since Hachimi has already done the heavy lifting
    log_message("Hachimi detected - initializing character replacement immediately");

    // Initialize IL2CPP helper
    if (il2cpp_helper::initialize()) {
        log_message("IL2CPP helper initialized successfully");

        // Initialize character replacer
        if (character_replacer::initialize()) {
            log_message("Character replacer initialized successfully");
            g_hook_ready = true;
        } else {
            log_message("Failed to initialize character replacer");
        }
    } else {
        log_message("Failed to initialize IL2CPP helper");
    }

    return true;
}

void uninit_hook() {
    if (!mh_initialized) {
        return;
    }

    log_message("Uninitializing hooks");
    character_replacer::uninitialize();
    
    MH_DisableHook(MH_ALL_HOOKS);
    MH_Uninitialize();
    mh_initialized = false;
}

// Main attach/detach functions
void attach() {
    log_message("UmaCharReplacer attaching to process");
    
    // Load configuration
    config::load_config();
    config::reload_config();

    // Create debug console if enabled
    if (g_enable_console) {
        create_debug_console();
    }

    // Initialize hooks
    if (!init_hook()) {
        log_message("Failed to initialize hooks");
        return;
    }

    log_message("UmaCharReplacer attached successfully");
}

void detach() {
    log_message("UmaCharReplacer detaching from process");
    uninit_hook();
}
