# Configuration Guide for UmaCharReplacer

This document explains how to configure UmaCharReplacer to replace character models in Umamusume: Pretty Derby.

## Configuration File

The configuration is stored in `hachimi/ucrconfig.json` relative to the game executable (`umamusume.exe`) directory.

## Configuration Structure

```json
{
    "replaceHomeStandChar": {
        "enable": false,
        "data": [
            {
                "origCharId": 1046,
                "newChrId": 2001,
                "newClothId": 200101
            }
        ]
    },
    "replaceGlobalChar": {
        "enable": false,
        "replaceUniversal": true,
        "data": [
            {
                "origCharId": 1001,
                "newChrId": 1046,
                "newClothId": 104601,
                "replaceMini": false
            }
        ]
    },
    "enableConsole": false,
    "enableLogging": true
}
```

## Configuration Options

### Home Stand Character Replacement

Controls character replacement specifically on the home screen.

- **`enable`** (boolean): Enable/disable home stand character replacement
- **`data`** (array): List of character replacement configurations

Each replacement entry contains:
- **`origCharId`** (integer): Original character ID to replace
- **`newChrId`** (integer): New character ID to use as replacement
- **`newClothId`** (integer): Cloth/outfit ID for the replacement character

### Global Character Replacement

Controls character replacement throughout the game (races, training, stories, etc.).

- **`enable`** (boolean): Enable/disable global character replacement
- **`replaceUniversal`** (boolean): Whether to replace universal outfits as well
- **`data`** (array): List of character replacement configurations

Each replacement entry contains:
- **`origCharId`** (integer): Original character ID to replace
- **`newChrId`** (integer): New character ID to use as replacement
- **`newClothId`** (integer): Cloth/outfit ID for the replacement character
- **`replaceMini`** (boolean): Whether to also replace mini character models

### General Settings

- **`enableConsole`** (boolean): Show debug console window (useful for troubleshooting)
- **`enableLogging`** (boolean): Enable logging to hachimi/UmaCharReplacer.log file

## Character and Cloth ID Reference

### Common Character IDs

| Character Name | Character ID |
|----------------|--------------|
| Special Week | 1001 |
| Silence Suzuka | 1002 |
| Tokai Teio | 1003 |
| Maruzensky | 1004 |
| Fuji Kiseki | 1005 |
| Oguri Cap | 1006 |
| Gold Ship | 1007 |
| Vodka | 1008 |
| Daiwa Scarlet | 1009 |
| Taiki Shuttle | 1010 |
| Grass Wonder | 1011 |
| El Condor Pasa | 1012 |
| T.M. Opera O | 1013 |
| Narita Brian | 1014 |
| Symboli Rudolf | 1015 |
| Air Groove | 1016 |
| Agnes Digital | 1017 |
| Seiun Sky | 1018 |
| Haru Urara | 1019 |
| King Halo | 1020 |
| Mihono Bourbon | 1021 |
| Mejiro McQueen | 1022 |
| Rice Shower | 1023 |
| Silence Suzuka | 1024 |
| Smart Falcon | 1046 |
| Trainer (Male) | 2001 |
| Trainer (Female) | 2002 |

### Cloth ID Format

Cloth IDs typically follow the pattern: `[CharacterID][OutfitNumber]`

Examples:
- `104601`: Smart Falcon's racing outfit
- `104602`: Smart Falcon's casual outfit
- `100101`: Special Week's racing outfit
- `200101`: Male trainer's default outfit

### Special Notes

- Character ID `9001` cannot be replaced in home stand mode
- Universal outfits (cloth IDs < 100000) are only replaced if `replaceUniversal` is true
- Mini character replacement only applies to global replacement mode

## Example Configurations

### Replace Smart Falcon with Trainer on Home Screen

```json
{
    "replaceHomeStandChar": {
        "enable": true,
        "data": [
            {
                "origCharId": 1046,
                "newChrId": 2001,
                "newClothId": 200101
            }
        ]
    },
    "replaceGlobalChar": {
        "enable": false,
        "replaceUniversal": true,
        "data": []
    },
    "enableConsole": false,
    "enableLogging": true
}
```

### Replace Multiple Characters Globally

```json
{
    "replaceHomeStandChar": {
        "enable": false,
        "data": []
    },
    "replaceGlobalChar": {
        "enable": true,
        "replaceUniversal": true,
        "data": [
            {
                "origCharId": 1001,
                "newChrId": 1046,
                "newClothId": 104601,
                "replaceMini": false
            },
            {
                "origCharId": 1002,
                "newChrId": 2001,
                "newClothId": 200101,
                "replaceMini": true
            }
        ]
    },
    "enableConsole": false,
    "enableLogging": true
}
```

### Debug Configuration

For troubleshooting, enable console and logging:

```json
{
    "replaceHomeStandChar": {
        "enable": false,
        "data": []
    },
    "replaceGlobalChar": {
        "enable": false,
        "replaceUniversal": true,
        "data": []
    },
    "enableConsole": true,
    "enableLogging": true
}
```

## Troubleshooting

### Configuration Not Loading

1. Ensure `hachimi/ucrconfig.json` exists in the game directory
2. Check that the JSON syntax is valid (use a JSON validator)
3. Enable logging and check `hachimi/UmaCharReplacer.log` for error messages

### Replacements Not Working

1. Verify character and cloth IDs are correct
2. Enable console to see debug messages
3. Check that the replacement is enabled in the configuration
4. Ensure the target character ID is not restricted (like 9001 for home stand)

### Game Crashes

1. Disable all replacements and test
2. Check that cloth IDs are valid for the target character
3. Try with `replaceUniversal` set to false
4. Verify the DLL is properly installed

## Advanced Configuration

### Finding Character and Cloth IDs

Character and cloth IDs can be found through:
1. Game database files
2. Community resources and wikis
3. Reverse engineering tools (for advanced users)
4. Trial and error with known working IDs

### Performance Considerations

- Minimize the number of replacements for better performance
- Use specific replacements rather than global ones when possible
- Disable mini character replacement if not needed

## Configuration Validation

The tool will validate the configuration on startup and log any errors. Common validation errors include:

- Invalid JSON syntax
- Missing required fields
- Invalid data types
- Out-of-range values

Always check the log file if replacements are not working as expected.
