#include "config.hpp"
#include <fstream>
#include <iostream>

namespace config {
    Config g_config;

    void to_json(nlohmann::json& j, const CharacterReplaceData& data) {
        j = nlohmann::json{
            {"origCharId", data.origCharId},
            {"newChrId", data.newChrId},
            {"newClothId", data.newClothId},
            {"replaceMini", data.replaceMini}
        };
    }

    void from_json(const nlohmann::json& j, CharacterReplaceData& data) {
        j.at("origCharId").get_to(data.origCharId);
        j.at("newChrId").get_to(data.newChrId);
        j.at("newClothId").get_to(data.newClothId);
        if (j.contains("replaceMini")) {
            j.at("replaceMini").get_to(data.replaceMini);
        }
    }

    void to_json(nlohmann::json& j, const HomeStandCharConfig& config) {
        j = nlohmann::json{
            {"enable", config.enable},
            {"data", config.data}
        };
    }

    void from_json(const nlohmann::json& j, HomeStandCharConfig& config) {
        j.at("enable").get_to(config.enable);
        j.at("data").get_to(config.data);
    }

    void to_json(nlohmann::json& j, const GlobalCharConfig& config) {
        j = nlohmann::json{
            {"enable", config.enable},
            {"replaceUniversal", config.replaceUniversal},
            {"data", config.data}
        };
    }

    void from_json(const nlohmann::json& j, GlobalCharConfig& config) {
        j.at("enable").get_to(config.enable);
        if (j.contains("replaceUniversal")) {
            j.at("replaceUniversal").get_to(config.replaceUniversal);
        }
        j.at("data").get_to(config.data);
    }

    void to_json(nlohmann::json& j, const Config& config) {
        j = nlohmann::json{
            {"replaceHomeStandChar", config.replaceHomeStandChar},
            {"replaceGlobalChar", config.replaceGlobalChar},
            {"enableConsole", config.enableConsole},
            {"enableLogging", config.enableLogging}
        };
    }

    void from_json(const nlohmann::json& j, Config& config) {
        j.at("replaceHomeStandChar").get_to(config.replaceHomeStandChar);
        j.at("replaceGlobalChar").get_to(config.replaceGlobalChar);
        if (j.contains("enableConsole")) {
            j.at("enableConsole").get_to(config.enableConsole);
        }
        if (j.contains("enableLogging")) {
            j.at("enableLogging").get_to(config.enableLogging);
        }
    }

    bool create_default_config() {
        try {
            // Create default configuration
            Config default_config;
            // default_config already has default values from struct initialization

            nlohmann::json j = default_config;

            std::ofstream config_file("hachimi/ucrconfig.json");
            if (!config_file.is_open()) {
                log_message("Error: Could not create default hachimi/ucrconfig.json file");
                return false;
            }

            config_file << j.dump(4); // Pretty print with 4-space indentation
            config_file.close();

            log_message("Created default hachimi/ucrconfig.json file");
            return true;
        }
        catch (const std::exception& e) {
            log_message("Error creating default config: " + std::string(e.what()));
            return false;
        }
    }

    bool load_config() {
        try {
            std::ifstream config_file("hachimi/ucrconfig.json");
            if (!config_file.is_open()) {
                log_message("Warning: hachimi/ucrconfig.json not found, creating default configuration");

                // Create default config file
                if (create_default_config()) {
                    // Try to load the newly created file
                    config_file.open("hachimi/ucrconfig.json");
                    if (!config_file.is_open()) {
                        log_message("Error: Could not open newly created hachimi/ucrconfig.json");
                        return false;
                    }
                } else {
                    log_message("Using default configuration in memory");
                    return false;
                }
            }

            nlohmann::json j;
            config_file >> j;
            g_config = j.get<Config>();

            log_message("Configuration loaded successfully");
            return true;
        }
        catch (const std::exception& e) {
            log_message("Error loading config: " + std::string(e.what()));
            return false;
        }
    }

    void reload_config() {
        load_config();
        
        // Update global variables
        g_enable_home_char_replace = g_config.replaceHomeStandChar.enable;
        g_enable_global_char_replace = g_config.replaceGlobalChar.enable;
        g_global_char_replace_Universal = g_config.replaceGlobalChar.replaceUniversal;
        g_enable_console = g_config.enableConsole;
        g_enable_logging = g_config.enableLogging;

        // Clear and rebuild replacement maps
        g_home_char_replace.clear();
        g_global_char_replace.clear();
        g_global_mini_char_replace.clear();

        // Build home character replacement map
        for (const auto& data : g_config.replaceHomeStandChar.data) {
            g_home_char_replace.emplace(data.origCharId, 
                std::make_pair(data.newChrId, data.newClothId));
        }

        // Build global character replacement map
        for (const auto& data : g_config.replaceGlobalChar.data) {
            if (data.replaceMini) {
                g_global_mini_char_replace.emplace(data.origCharId,
                    std::make_pair(data.newChrId, data.newClothId));
            }
            g_global_char_replace.emplace(data.origCharId,
                std::make_pair(data.newChrId, data.newClothId));
        }

        log_message("Configuration reloaded");
    }

    const Config& get_config() {
        return g_config;
    }
}
