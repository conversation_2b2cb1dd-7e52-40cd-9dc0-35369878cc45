use std::sync::atomic;

use crate::{core::<PERSON><PERSON><PERSON>, il2cpp::{api::il2cpp_resolve_icall, types::*}};

type SetTargetFrameRateFn = extern "C" fn(value: i32);
pub extern "C" fn set_targetFrameRate(mut value: i32) {
    let target_fps = Hachimi::instance().target_fps.load(atomic::Ordering::Relaxed);
    if target_fps != -1 {
        value = target_fps;
    }
    get_orig_fn!(set_targetFrameRate, SetTargetFrameRateFn)(value);
}

pub fn init(_UnityEngine_CoreModule: *const Il2CppImage) {
    let set_targetFrameRate_addr = il2cpp_resolve_icall(
        c"UnityEngine.Application::set_targetFrameRate(System.Int32)".as_ptr()
    );

    new_hook!(set_targetFrameRate_addr, set_targetFrameRate);
}