<div align="center">

# Trainers' Legend G
> 赛马娘 Pretty Derby（DMM版）本地化插件

[![Download](https://img.shields.io/github/v/release/MinamiChiwa/umamusume-localify-zh-CN?color=blue&logoColor=white&label=Download&logo=DocuSign)](https://github.com/MinamiChiwa/Trainers-Legend-G/releases/latest)
[![Chat](https://img.shields.io/badge/Join-QQ%E9%A2%91%E9%81%93-blue?logo=tencent-qq&logoColor=white)](https://qun.qq.com/qqweb/qunpro/share?_wv=3&_wwv=128&inviteCode=1olqdK&from=246610&biz=ka)
[![Discord](https://img.shields.io/discord/973208860217200653?color=blue&label=Discord&logo=Discord&logoColor=white)](https://discord.com/invite/TBCSv5hU69)

简体中文 | [繁體中文](https://github.com/yotv2000tw/Trainers-Legend-G-TRANS-zh-tw/) | [English](readme_EN.md) | [日本語](readme_JA.md)

</div>
 

# 简介

- 本插件旨在提升简中区赛马娘 Pretty Derby（DMM版）玩家游戏体验，提供文本汉化、帧率与分辨率限制解锁、替换图片资源与字体、Live自由镜头等多种拓展功能。
- 本插件另有独立发行的繁体中文版，见 [`Trainers-Legend-G-TRANS-zh-tw`](https://github.com/yotv2000tw/Trainers-Legend-G-TRANS-zh-tw) 。
- 本插件在 [`umamusume-localify`](https://github.com/GEEKiDoS/umamusume-localify) （原作者@GEEKiDoS）基础之上开发。

**更多关于插件使用的介绍与说明，请查看 [`wiki页面`](https://github.com/MinamiChiwa/Trainers-Legend-G/wiki) 。**

# 特别说明

- 使用外部工具本身为违反游戏使用条款的行为。 **若使用本插件后账号被警告/封禁，造成的后果由用户自行担责。**
- 本插件仅提供 **部分内容汉化** 。具体内容与进度可查看 [翻译进度表](https://github.com/MinamiChiwa/Trainers-Legend-G-TRANS/blob/master/translation-progress.md) 。
- 若以任何方式（录制视频、提取文本等）转载或大段搬运译文，用户 **必须注明** 原译者或 [贡献者列表及授权参考列表](https://github.com/MinamiChiwa/Trainers-Legend-G-TRANS/blob/master/translation-progress.md) 。
- **使用本插件完全免费。** 根据中华人民共和国《计算机软件保护条例》第十七条规定：「为了学习和研究软件内含的设计思想和原理，通过安装、显示、传输或者存储软件等方式使用软件的，可以不经软件著作权人许可，不向其支付报酬。」本插件仅供用户交流学习与研究使用，用户本人下载后不能用作商业或非法用途，需在24小时之内删除，否则后果均由用户承担责任。

# 贡献者（GitHub）
## 本体仓库
<a href="https://github.com/MinamiChiwa/Trainers-Legend-G/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=MinamiChiwa/Trainers-Legend-G" />
</a>

## 译文仓库
<a href="https://github.com/MinamiChiwa/Trainers-Legend-G-TRANS/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=MinamiChiwa/Trainers-Legend-G-TRANS" />
</a>

亦有部分译文贡献者来自其他平台而非 GitHub ，详情请见 [`翻译进度表`](https://github.com/MinamiChiwa/Trainers-Legend-G-TRANS/blob/master/translation-progress.md) 。

# 赞助
本项目已于2023年1月起停止接受赞助。您仍可查看 [`赞助说明及支出明细`](donate_readme.md) 。
