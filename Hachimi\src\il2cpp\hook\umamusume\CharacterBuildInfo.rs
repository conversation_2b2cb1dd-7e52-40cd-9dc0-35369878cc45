use crate::{core::{<PERSON><PERSON><PERSON>, hachimi::CharacterReplaceData, log::*}, il2cpp::{symbols::get_method_addr, types::*}};
use std::{collections::HashMap, sync::atomic::{AtomicBool, Ordering}};

// Global state for character replacement toggles (controlled by menu)
static HOME_REPLACEMENT_ENABLED: AtomicBool = AtomicBool::new(false);
static GLOBAL_REPLACEMENT_ENABLED: AtomicBool = AtomicBool::new(false);

// Controller types for different game contexts
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum UmaControllerType {
    HomeStand = 0x1919810,
    Race = 0x191981A,
    Training = 0x191981B,
    Story = 0x191981C,
    Orig = 0x0,
}

// Public functions for menu control
pub fn set_home_replacement_enabled(enabled: bool) {
    HOME_REPLACEMENT_ENABLED.store(enabled, Ordering::Relaxed);
    info!("[CharacterBuildInfo] Home replacement {}", if enabled { "enabled" } else { "disabled" });
}

pub fn set_global_replacement_enabled(enabled: bool) {
    GLOBAL_REPLACEMENT_ENABLED.store(enabled, Ordering::Relaxed);
    info!("[CharacterBuildInfo] Global replacement {}", if enabled { "enabled" } else { "disabled" });
}

pub fn is_home_replacement_enabled() -> bool {
    HOME_REPLACEMENT_ENABLED.load(Ordering::Relaxed)
}

pub fn is_global_replacement_enabled() -> bool {
    GLOBAL_REPLACEMENT_ENABLED.load(Ordering::Relaxed)
}

// Helper function to build replacement maps from config
fn build_replacement_maps(data: &[CharacterReplaceData]) -> HashMap<i32, (i32, i32)> {
    data.iter()
        .map(|item| (item.orig_char_id, (item.new_chr_id, item.new_cloth_id)))
        .collect()
}

// Get head ID from dress ID (matching Trainers-Legend-G logic)
fn get_head_id_from_dress_id(dress_id: i32) -> i32 {
    // This should ideally use UmaDatabase, but for now use the same logic as original
    if dress_id >= 100000 {
        (dress_id / 100) * 100 + 1
    } else {
        dress_id
    }
}

// Validate character and dress IDs
fn validate_character_replacement(char_id: i32, dress_id: i32) -> bool {
    // Basic validation - character IDs should be reasonable
    if char_id <= 0 || char_id > 9999 {
        error!("[CharacterBuildInfo] Invalid character ID: {}", char_id);
        return false;
    }

    // Dress IDs should be reasonable
    if dress_id <= 0 || dress_id > 999999 {
        error!("[CharacterBuildInfo] Invalid dress ID: {}", dress_id);
        return false;
    }

    true
}

// Core character replacement logic
fn replace_character_controller(
    hachimi: &Hachimi,
    chara_id: &mut i32,
    dress_id: &mut i32,
    head_id: &mut i32,
    controller_type: UmaControllerType,
) -> bool {
    let config = hachimi.config.load();
    let char_config = &config.character_replacement;

    info!("[CharacterBuildInfo] Replacement check: chara_id={}, controller_type={:?}", *chara_id, controller_type);
    info!("[CharacterBuildInfo] Config: home_enable={}, global_enable={}",
          char_config.replace_home_stand_char.enable, char_config.replace_global_char.enable);

    let mut replace_dress = true;
    if (*dress_id < 100000) && !char_config.replace_global_char.replace_universal {
        replace_dress = false;
    }

    // Home stand character replacement
    if char_config.replace_home_stand_char.enable &&
       controller_type == UmaControllerType::HomeStand &&
       HOME_REPLACEMENT_ENABLED.load(Ordering::Relaxed) {

        info!("[CharacterBuildInfo] Checking home replacement for chara_id={}", *chara_id);

        if *chara_id == 9001 {
            info!("[CharacterBuildInfo] Skipping chara_id 9001 (not replaceable at home)");
            return false; // Can't replace this at home
        }

        let home_map = build_replacement_maps(&char_config.replace_home_stand_char.data);
        info!("[CharacterBuildInfo] Home replacement map has {} entries", home_map.len());

        if let Some((new_char_id, new_cloth_id)) = home_map.get(chara_id) {
            if validate_character_replacement(*new_char_id, *new_cloth_id) {
                info!("[CharacterBuildInfo] Home replacement found: {} -> {}, dress -> {}",
                      *chara_id, *new_char_id, *new_cloth_id);
                *chara_id = *new_char_id;
                *dress_id = *new_cloth_id;
                *head_id = get_head_id_from_dress_id(*dress_id);
                return true;
            } else {
                error!("[CharacterBuildInfo] Invalid home replacement: {} -> {}, dress -> {}",
                       *chara_id, *new_char_id, *new_cloth_id);
            }
        } else {
            info!("[CharacterBuildInfo] No home replacement found for chara_id={}", *chara_id);
        }
    }

    // Global character replacement
    if char_config.replace_global_char.enable &&
       matches!(controller_type, UmaControllerType::Race | UmaControllerType::Training | UmaControllerType::Story | UmaControllerType::Orig) &&
       GLOBAL_REPLACEMENT_ENABLED.load(Ordering::Relaxed) {

        info!("[CharacterBuildInfo] Checking global replacement for chara_id={}", *chara_id);

        let global_map = build_replacement_maps(&char_config.replace_global_char.data);
        info!("[CharacterBuildInfo] Global replacement map has {} entries", global_map.len());

        if let Some((new_char_id, new_cloth_id)) = global_map.get(chara_id) {
            if validate_character_replacement(*new_char_id, *new_cloth_id) {
                info!("[CharacterBuildInfo] Global replacement found: {} -> {}, dress -> {}",
                      *chara_id, *new_char_id, *new_cloth_id);
                *chara_id = *new_char_id;
                if replace_dress {
                    *dress_id = *new_cloth_id;
                }
                *head_id = get_head_id_from_dress_id(*dress_id);
                return true;
            } else {
                error!("[CharacterBuildInfo] Invalid global replacement: {} -> {}, dress -> {}",
                       *chara_id, *new_char_id, *new_cloth_id);
            }
        } else {
            info!("[CharacterBuildInfo] No global replacement found for chara_id={}", *chara_id);
        }
    }

    false
}

// Hook functions
type CharacterBuildInfoRebuildFn = extern "C" fn(this: *mut Il2CppObject);
extern "C" fn CharacterBuildInfo_Rebuild(this: *mut Il2CppObject) {
    info!("[CharacterBuildInfo] Rebuild called");

    // Try to read and modify character fields like the original does
    // This is a simplified version - the original uses proper field resolution
    unsafe {
        // For now, just call the original function
        // TODO: Implement proper field reading/writing when field offsets are available
        info!("[CharacterBuildInfo] Calling original Rebuild (field modification not implemented yet)");
    }

    get_orig_fn!(CharacterBuildInfo_Rebuild, CharacterBuildInfoRebuildFn)(this);
}

type CharacterBuildInfoCtor0Fn = extern "C" fn(
    this: *mut Il2CppObject, chara_id: i32, dress_id: i32, controller_type: i32,
    head_id: i32, zekken: i32, mob_id: i32, back_dancer_color_id: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool
);
extern "C" fn CharacterBuildInfo_ctor_0(
    this: *mut Il2CppObject, mut chara_id: i32, mut dress_id: i32, controller_type: i32,
    mut head_id: i32, zekken: i32, mob_id: i32, back_dancer_color_id: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool
) {
    info!("[CharacterBuildInfo] ctor_0 called: chara_id={}, dress_id={}, controller_type=0x{:x}, head_id={}",
          chara_id, dress_id, controller_type, head_id);

    let uma_controller_type = match controller_type {
        0x1919810 => UmaControllerType::HomeStand,
        0x191981A => UmaControllerType::Race,
        0x191981B => UmaControllerType::Training,
        0x191981C => UmaControllerType::Story,
        _ => UmaControllerType::Orig,
    };

    let original_chara_id = chara_id;
    let original_dress_id = dress_id;
    let original_head_id = head_id;

    let hachimi = Hachimi::instance();
    let replaced = replace_character_controller(&hachimi, &mut chara_id, &mut dress_id, &mut head_id, uma_controller_type);

    if replaced {
        info!("[CharacterBuildInfo] Replacement applied: char {} -> {}, dress {} -> {}, head {} -> {}",
              original_chara_id, chara_id, original_dress_id, dress_id, original_head_id, head_id);
    } else {
        info!("[CharacterBuildInfo] No replacement applied for chara_id={}", chara_id);
    }

    // Use motion_dress_id = dress_id like the original does
    let final_motion_dress_id = dress_id;

    get_orig_fn!(CharacterBuildInfo_ctor_0, CharacterBuildInfoCtor0Fn)(
        this, chara_id, dress_id, controller_type, head_id, zekken, mob_id,
        back_dancer_color_id, is_use_dress_data_head_model_sub_id, audience_id,
        final_motion_dress_id, is_enable_model_cache
    );
}

type CharacterBuildInfoCtor1Fn = extern "C" fn(
    this: *mut Il2CppObject, card_id: i32, chara_id: i32, dress_id: i32,
    controller_type: i32, head_id: i32, zekken: i32, mob_id: i32,
    back_dancer_color_id: i32, override_cloth_category: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool, chara_dress_color_set_id: i32
);
extern "C" fn CharacterBuildInfo_ctor_1(
    this: *mut Il2CppObject, card_id: i32, mut chara_id: i32, mut dress_id: i32,
    controller_type: i32, mut head_id: i32, zekken: i32, mob_id: i32,
    back_dancer_color_id: i32, override_cloth_category: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool, chara_dress_color_set_id: i32
) {
    info!("[CharacterBuildInfo] ctor_1 called: card_id={}, chara_id={}, dress_id={}, controller_type=0x{:x}, head_id={}",
          card_id, chara_id, dress_id, controller_type, head_id);

    let uma_controller_type = match controller_type {
        0x1919810 => UmaControllerType::HomeStand,
        0x191981A => UmaControllerType::Race,
        0x191981B => UmaControllerType::Training,
        0x191981C => UmaControllerType::Story,
        _ => UmaControllerType::Orig,
    };

    let original_chara_id = chara_id;
    let original_dress_id = dress_id;
    let original_head_id = head_id;

    let hachimi = Hachimi::instance();
    let replaced = replace_character_controller(&hachimi, &mut chara_id, &mut dress_id, &mut head_id, uma_controller_type);

    if replaced {
        info!("[CharacterBuildInfo] ctor_1 replacement applied: char {} -> {}, dress {} -> {}, head {} -> {}",
              original_chara_id, chara_id, original_dress_id, dress_id, original_head_id, head_id);
    } else {
        info!("[CharacterBuildInfo] ctor_1 no replacement applied for chara_id={}", chara_id);
    }

    // Use motion_dress_id = dress_id like the original does
    let final_motion_dress_id = dress_id;

    get_orig_fn!(CharacterBuildInfo_ctor_1, CharacterBuildInfoCtor1Fn)(
        this, card_id, chara_id, dress_id, controller_type, head_id, zekken, mob_id,
        back_dancer_color_id, override_cloth_category, is_use_dress_data_head_model_sub_id,
        audience_id, final_motion_dress_id, is_enable_model_cache, chara_dress_color_set_id
    );
}

pub fn init(umamusume: *const Il2CppImage) {
    info!("[CharacterBuildInfo] Initializing character replacement hooks");

    // Try to get the CharacterBuildInfo class
    let CharacterBuildInfo = match crate::il2cpp::symbols::get_class(umamusume, c"Gallop", c"CharacterBuildInfo") {
        Ok(v) => v,
        Err(e) => {
            error!("[CharacterBuildInfo] Failed to get CharacterBuildInfo class: {}", e);
            return;
        }
    };
    info!("[CharacterBuildInfo] CharacterBuildInfo class found");

    let CharacterBuildInfo_Rebuild_addr = get_method_addr(CharacterBuildInfo, c"Rebuild", 0);
    let CharacterBuildInfo_ctor_0_addr = get_method_addr(CharacterBuildInfo, c".ctor", 11);
    let CharacterBuildInfo_ctor_1_addr = get_method_addr(CharacterBuildInfo, c".ctor", 14);

    info!("[CharacterBuildInfo] Method addresses: Rebuild=0x{:x}, ctor_0=0x{:x}, ctor_1=0x{:x}",
          CharacterBuildInfo_Rebuild_addr, CharacterBuildInfo_ctor_0_addr, CharacterBuildInfo_ctor_1_addr);

    // Install hooks manually for debugging
    let hachimi = Hachimi::instance();

    if CharacterBuildInfo_Rebuild_addr != 0 {
        if let Err(e) = hachimi.interceptor.hook(CharacterBuildInfo_Rebuild_addr, CharacterBuildInfo_Rebuild as usize) {
            error!("[CharacterBuildInfo] Failed to hook Rebuild: {}", e);
        } else {
            info!("[CharacterBuildInfo] Rebuild hook installed");
        }
    }

    if CharacterBuildInfo_ctor_0_addr != 0 {
        if let Err(e) = hachimi.interceptor.hook(CharacterBuildInfo_ctor_0_addr, CharacterBuildInfo_ctor_0 as usize) {
            error!("[CharacterBuildInfo] Failed to hook ctor_0: {}", e);
        } else {
            info!("[CharacterBuildInfo] ctor_0 hook installed");
        }
    }

    if CharacterBuildInfo_ctor_1_addr != 0 {
        if let Err(e) = hachimi.interceptor.hook(CharacterBuildInfo_ctor_1_addr, CharacterBuildInfo_ctor_1 as usize) {
            error!("[CharacterBuildInfo] Failed to hook ctor_1: {}", e);
        } else {
            info!("[CharacterBuildInfo] ctor_1 hook installed");
        }
    }

    info!("[CharacterBuildInfo] Character replacement hooks installation completed");

    // Log current configuration
    let config = hachimi.config.load();
    let char_config = &config.character_replacement;

    info!("[CharacterBuildInfo] Configuration loaded:");
    info!("[CharacterBuildInfo]   Home replacement: enabled={}, {} entries",
          char_config.replace_home_stand_char.enable, char_config.replace_home_stand_char.data.len());
    info!("[CharacterBuildInfo]   Global replacement: enabled={}, {} entries, universal={}",
          char_config.replace_global_char.enable, char_config.replace_global_char.data.len(),
          char_config.replace_global_char.replace_universal);
}
