use crate::{core::{<PERSON><PERSON><PERSON>, hachimi::CharacterReplaceData}, il2cpp::{symbols::get_method_addr, types::*}};
use std::collections::HashMap;

// Controller types for different game contexts
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum UmaControllerType {
    HomeStand = 0x1919810,
    Race = 0x191981A,
    Training = 0x191981B,
    Story = 0x191981C,
    Orig = 0x0,
}

// Helper function to build replacement maps from config
fn build_replacement_maps(data: &[CharacterReplaceData]) -> HashMap<i32, (i32, i32)> {
    data.iter()
        .map(|item| (item.orig_char_id, (item.new_chr_id, item.new_cloth_id)))
        .collect()
}

// Get head ID from dress ID (simplified version)
fn get_head_id_from_dress_id(dress_id: i32) -> i32 {
    if dress_id >= 100000 {
        (dress_id / 100) * 100 + 1
    } else {
        dress_id
    }
}

// Core character replacement logic
fn replace_character_controller(
    hachimi: &Ha<PERSON><PERSON>,
    chara_id: &mut i32,
    dress_id: &mut i32,
    head_id: &mut i32,
    controller_type: UmaControllerType,
) -> bool {
    let config = hachimi.config.load();
    let char_config = &config.character_replacement;

    let mut replace_dress = true;
    if (*dress_id < 100000) && !char_config.replace_global_char.replace_universal {
        replace_dress = false;
    }

    // Home stand character replacement
    if char_config.replace_home_stand_char.enable && controller_type == UmaControllerType::HomeStand {
        if *chara_id == 9001 {
            return false; // Can't replace this at home
        }

        let home_map = build_replacement_maps(&char_config.replace_home_stand_char.data);
        if let Some((new_char_id, new_cloth_id)) = home_map.get(chara_id) {
            *chara_id = *new_char_id;
            *dress_id = *new_cloth_id;
            *head_id = get_head_id_from_dress_id(*dress_id);
            return true;
        }
    }

    // Global character replacement
    if char_config.replace_global_char.enable && matches!(controller_type,
        UmaControllerType::Race | UmaControllerType::Training | UmaControllerType::Story | UmaControllerType::Orig) {

        let global_map = build_replacement_maps(&char_config.replace_global_char.data);
        if let Some((new_char_id, new_cloth_id)) = global_map.get(chara_id) {
            *chara_id = *new_char_id;
            if replace_dress {
                *dress_id = *new_cloth_id;
            }
            *head_id = get_head_id_from_dress_id(*dress_id);
            return true;
        }
    }

    false
}

// Hook functions
type CharacterBuildInfoRebuildFn = extern "C" fn(this: *mut Il2CppObject);
extern "C" fn CharacterBuildInfo_Rebuild(this: *mut Il2CppObject) {
    // Get field offsets (these would need to be resolved properly)
    // For now, we'll call the original function
    // TODO: Implement field reading/writing using Hachimi's IL2CPP utilities
    
    get_orig_fn!(CharacterBuildInfo_Rebuild, CharacterBuildInfoRebuildFn)(this);
}

type CharacterBuildInfoCtor0Fn = extern "C" fn(
    this: *mut Il2CppObject, chara_id: i32, dress_id: i32, controller_type: i32,
    head_id: i32, zekken: i32, mob_id: i32, back_dancer_color_id: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool
);
extern "C" fn CharacterBuildInfo_ctor_0(
    this: *mut Il2CppObject, mut chara_id: i32, mut dress_id: i32, controller_type: i32,
    mut head_id: i32, zekken: i32, mob_id: i32, back_dancer_color_id: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool
) {
    let uma_controller_type = match controller_type {
        0x1919810 => UmaControllerType::HomeStand,
        0x191981A => UmaControllerType::Race,
        0x191981B => UmaControllerType::Training,
        0x191981C => UmaControllerType::Story,
        _ => UmaControllerType::Orig,
    };

    let hachimi = Hachimi::instance();
    replace_character_controller(&hachimi, &mut chara_id, &mut dress_id, &mut head_id, uma_controller_type);

    get_orig_fn!(CharacterBuildInfo_ctor_0, CharacterBuildInfoCtor0Fn)(
        this, chara_id, dress_id, controller_type, head_id, zekken, mob_id,
        back_dancer_color_id, is_use_dress_data_head_model_sub_id, audience_id,
        motion_dress_id, is_enable_model_cache
    );
}

type CharacterBuildInfoCtor1Fn = extern "C" fn(
    this: *mut Il2CppObject, card_id: i32, chara_id: i32, dress_id: i32,
    controller_type: i32, head_id: i32, zekken: i32, mob_id: i32,
    back_dancer_color_id: i32, override_cloth_category: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool, chara_dress_color_set_id: i32
);
extern "C" fn CharacterBuildInfo_ctor_1(
    this: *mut Il2CppObject, card_id: i32, mut chara_id: i32, mut dress_id: i32,
    controller_type: i32, mut head_id: i32, zekken: i32, mob_id: i32,
    back_dancer_color_id: i32, override_cloth_category: i32,
    is_use_dress_data_head_model_sub_id: bool, audience_id: i32,
    motion_dress_id: i32, is_enable_model_cache: bool, chara_dress_color_set_id: i32
) {
    let uma_controller_type = match controller_type {
        0x1919810 => UmaControllerType::HomeStand,
        0x191981A => UmaControllerType::Race,
        0x191981B => UmaControllerType::Training,
        0x191981C => UmaControllerType::Story,
        _ => UmaControllerType::Orig,
    };

    let hachimi = Hachimi::instance();
    replace_character_controller(&hachimi, &mut chara_id, &mut dress_id, &mut head_id, uma_controller_type);

    get_orig_fn!(CharacterBuildInfo_ctor_1, CharacterBuildInfoCtor1Fn)(
        this, card_id, chara_id, dress_id, controller_type, head_id, zekken, mob_id,
        back_dancer_color_id, override_cloth_category, is_use_dress_data_head_model_sub_id,
        audience_id, motion_dress_id, is_enable_model_cache, chara_dress_color_set_id
    );
}

pub fn init(umamusume: *const Il2CppImage) {
    get_class_or_return!(umamusume, Gallop, CharacterBuildInfo);

    let CharacterBuildInfo_Rebuild_addr = get_method_addr(CharacterBuildInfo, c"Rebuild", 0);
    let CharacterBuildInfo_ctor_0_addr = get_method_addr(CharacterBuildInfo, c".ctor", 11);
    let CharacterBuildInfo_ctor_1_addr = get_method_addr(CharacterBuildInfo, c".ctor", 14);

    new_hook!(CharacterBuildInfo_Rebuild_addr, CharacterBuildInfo_Rebuild);
    new_hook!(CharacterBuildInfo_ctor_0_addr, CharacterBuildInfo_ctor_0);
    new_hook!(CharacterBuildInfo_ctor_1_addr, CharacterBuildInfo_ctor_1);
}
