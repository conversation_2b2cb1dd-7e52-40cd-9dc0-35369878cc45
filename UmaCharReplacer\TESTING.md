# Testing and Validation Guide

This document outlines the testing procedures and validation steps for UmaCharReplacer.

## ⚠️ Important Notice

**The current implementation requires additional work to be fully functional:**

1. **Function Address Resolution**: The `resolve_function_addresses()` function in `character_replacer.cpp` contains placeholder code and needs to be implemented with actual game function addresses.

2. **Game Version Compatibility**: Function addresses are specific to each game version and must be updated accordingly.

3. **Reverse Engineering Required**: Finding the correct function addresses requires reverse engineering skills and tools.

## Pre-Testing Requirements

### Before You Begin

1. **Backup Your Game**: Always backup your game installation before testing
2. **Backup Save Data**: Backup your save files and user data
3. **Test Environment**: Use a separate test installation if possible
4. **Version Compatibility**: Ensure you're testing with a supported game version

### Required Files

- `version.dll` (built from source)
- `version_orig.dll` (renamed original version.dll)
- `config.json` (configuration file)
- `UmaCharReplacer.log` (will be created automatically)

## Testing Phases

### Phase 1: Installation Testing

#### Test 1.1: DLL Proxy Functionality
1. Install the DLL as described in README.md
2. Launch the game
3. Verify the game starts without crashes
4. Check that `UmaCharReplacer.log` is created

**Expected Results:**
- Game launches normally
- Log file shows initialization messages
- No immediate crashes or errors

#### Test 1.2: Configuration Loading
1. Create a minimal `config.json` with all features disabled
2. Launch the game
3. Check log for configuration loading messages

**Expected Results:**
- "Configuration loaded successfully" in log
- No configuration errors

### Phase 2: Hook Installation Testing

#### Test 2.1: Hook Initialization
1. Enable console in config (`"enableConsole": true`)
2. Launch the game
3. Monitor console output for hook initialization

**Expected Results:**
- MinHook initialization success
- LoadLibraryW hook creation
- IL2CPP helper initialization (may fail without proper addresses)

#### Test 2.2: Game Ready Detection
1. Monitor log for "Game ready for hooking" message
2. Verify the message appears when cri_ware_unity.dll loads

**Expected Results:**
- Game ready message appears
- Hook removal after detection

### Phase 3: Character Replacement Testing

⚠️ **Note**: These tests will likely fail until function addresses are properly resolved.

#### Test 3.1: Home Stand Character Replacement
1. Configure a simple home character replacement:
   ```json
   {
       "replaceHomeStandChar": {
           "enable": true,
           "data": [
               {
                   "origCharId": 1001,
                   "newChrId": 1046,
                   "newClothId": 104601
               }
           ]
       }
   }
   ```
2. Launch the game and go to home screen
3. Check if Special Week (1001) is replaced with Smart Falcon (1046)

**Expected Results (when properly implemented):**
- Character model replacement occurs
- Log shows replacement messages
- No crashes or visual glitches

#### Test 3.2: Global Character Replacement
1. Configure global character replacement
2. Test in different game modes (races, training, stories)
3. Verify replacements work consistently

### Phase 4: Stability Testing

#### Test 4.1: Extended Play Session
1. Play the game for an extended period (30+ minutes)
2. Navigate through different game modes
3. Monitor for memory leaks or crashes

#### Test 4.2: Rapid Configuration Changes
1. Modify config.json while game is running
2. Test configuration reload functionality
3. Verify no memory corruption occurs

## Validation Checklist

### Functional Validation

- [ ] DLL loads without errors
- [ ] Configuration file is parsed correctly
- [ ] Hooks are installed successfully
- [ ] Character replacements work as configured
- [ ] Game remains stable during normal play
- [ ] Log file contains appropriate debug information

### Performance Validation

- [ ] No significant performance impact
- [ ] Memory usage remains stable
- [ ] No frame rate drops during character loading
- [ ] Hook overhead is minimal

### Compatibility Validation

- [ ] Works with current game version
- [ ] Compatible with game updates
- [ ] No conflicts with other mods/tools
- [ ] Proper cleanup on game exit

## Known Issues and Limitations

### Current Implementation Issues

1. **Function Address Resolution**: Not implemented - requires reverse engineering
2. **IL2CPP Field Access**: May fail without proper class/field resolution
3. **Game Version Dependency**: Addresses are version-specific

### Expected Behaviors

1. **Character ID 9001**: Cannot be replaced in home stand mode (by design)
2. **Universal Outfits**: Only replaced when `replaceUniversal` is true
3. **Mini Characters**: Only replaced when `replaceMini` is true

## Debugging and Troubleshooting

### Debug Configuration

For testing, use this debug configuration:

```json
{
    "replaceHomeStandChar": {
        "enable": false,
        "data": []
    },
    "replaceGlobalChar": {
        "enable": false,
        "replaceUniversal": true,
        "data": []
    },
    "enableConsole": true,
    "enableLogging": true
}
```

### Common Issues

#### Game Won't Start
- Check that `version_orig.dll` exists
- Verify DLL architecture matches game (x64)
- Check Windows Defender/antivirus exclusions

#### No Character Replacements
- Verify function addresses are correctly resolved
- Check that IL2CPP helper initialization succeeds
- Ensure character/cloth IDs are valid

#### Crashes During Character Loading
- Disable all replacements and test incrementally
- Check for invalid cloth IDs
- Verify hook installation didn't corrupt memory

### Log Analysis

Key log messages to look for:

```
[timestamp] UmaCharReplacer attaching to process
[timestamp] Configuration loaded successfully
[timestamp] MinHook initialized successfully
[timestamp] Game ready for hooking - initializing character replacement
[timestamp] IL2CPP helper initialized successfully
[timestamp] Character replacer initialized successfully
```

Error messages to watch for:

```
[timestamp] Failed to initialize MinHook
[timestamp] Failed to get GameAssembly.dll module
[timestamp] Failed to resolve function addresses
[timestamp] Warning: Function address resolution not implemented
```

## Test Data

### Safe Test Character IDs

These character IDs are generally safe for testing:
- 1001 (Special Week)
- 1002 (Silence Suzuka)
- 1046 (Smart Falcon)

### Safe Test Cloth IDs

- 100101 (Special Week racing outfit)
- 100201 (Silence Suzuka racing outfit)
- 104601 (Smart Falcon racing outfit)

## Reporting Issues

When reporting issues, include:

1. **Game Version**: Exact version number
2. **Configuration**: Your config.json file
3. **Log File**: Complete UmaCharReplacer.log
4. **Steps to Reproduce**: Detailed reproduction steps
5. **Expected vs Actual**: What you expected vs what happened
6. **System Info**: Windows version, hardware specs

## Next Steps for Full Implementation

To make this tool fully functional:

1. **Reverse Engineer Game Functions**: Find addresses for:
   - `CharacterBuildInfo::Rebuild`
   - `StoryCharacter3D::LoadModel`
   - `GetRaceDressId`

2. **Implement Pattern Scanning**: Create robust function finding
3. **Add Version Detection**: Support multiple game versions
4. **Enhance Error Handling**: Better error recovery and reporting
5. **Add More Hook Points**: Cover additional character loading scenarios

## Safety Recommendations

1. **Always backup** before testing
2. **Test incrementally** - start with simple configurations
3. **Monitor system resources** during testing
4. **Use test accounts** when possible
5. **Keep original files** for easy restoration

Remember: This tool is for educational purposes and should be used responsibly in accordance with the game's terms of service.
