<img align="left" width="80" height="80" src="assets/icon.png">

# Hachimi

[English](README.md) | 简体中文 | [繁體中文](README-zh_tw.md)

[![Discord 服务器](https://dcbadge.limes.pink/api/server/https://discord.gg/BVEt5FcxEn)](https://discord.gg/BVEt5FcxEn)

某赛马拟人化游戏的游戏增强与翻译插件。

<img height="400" src="assets/screenshot.jpg">

# ⚠️ 请不要分享这个仓库与 Hachimi 网站的相关链接
我们非常理解您希望帮助别人安装 Hachimi 来与其获得更好的游戏体验。但是，此项目本质上违反了游戏的服务条款，如果被游戏的开发商得知，他们肯定会希望这个项目灰飞烟灭。

您可以私下与别人分享，这完全没有问题，但是我们请求您不要在公开的网站上分享此项目的相关链接，也不要分享任何相关工具的链接。

如果进行分享宣传，这可能会破坏很多 Hachimi 用户的体验，请三思而后行。

### 如果您执意要分享
我们无法改变您的想法，但我们建议您在提及游戏本体时，使用 “UM:PD” 或 “某赛马拟人化游戏” 等隐语去代替游戏名字，这样能避免被搜索引擎剖析到。

# 功能特性
- **高质量翻译：** 内置先进的翻译功能系统，支持复数形式、序数词等自然语言特性，避免界面异常。支持自动翻译绝大多数游戏组件，无需手动修改资源文件！

    已支持组件：
    - 界面文本
    - master.mdb（技能名称、技能描述等）
    - 赛事剧情
    - 主线剧情/育成对话
    - 歌词
    - 纹理替换
    - 图集替换

    此外，Hachimi 并非单一语言专属翻译工具，其架构设计支持任意语言的完整适配。

- **便捷安装：** 安装即用。所有配置均在游戏内完成，无需外部程序。
- **翻译自动更新：** 内置更新器可在游戏运行时后台更新翻译，完成后即时生效无需重启。
- **内置控制面板：** 配置编辑器可直接在游戏内调整设置，无需退出游戏。
- **画质优化：** 解锁帧率限制、分辨率缩放等图形设置，充分释放设备性能。
- **跨平台支持：** 专为多平台设计，支持 Windows 和 Android 系统。

# 安装指南
请参阅[入门指南](https://hachimi.leadrdrk.com/docs/hachimi/getting-started.html)。

# 特别鸣谢
以下项目为 Hachimi 的开发奠定了重要基础，如果没有它们，Hachimi 不会有今天如此成就：

- [Trainers' Legend G](https://github.com/MinamiChiwa/Trainers-Legend-G)
- [umamusume-localify-android](https://github.com/Kimjio/umamusume-localify-android)
- [umamusume-localify](https://github.com/GEEKiDoS/umamusume-localify)
- [Carotenify](https://github.com/KevinVG207/Uma-Carotenify)
- [umamusu-translate](https://github.com/noccu/umamusu-translate)
- [frida-il2cpp-bridge](https://github.com/vfsfitvnm/frida-il2cpp-bridge)

# 许可证
[GNU GPLv3](LICENSE)
