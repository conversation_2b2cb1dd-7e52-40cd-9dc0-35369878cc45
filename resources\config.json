{"enableConsole": true, "enableLogger": false, "dumpStaticEntries": false, "maxFps": 60, "better60FPS": false, "highQuality": true, "virtual_resolution_multiple": 1.0, "enableVSync": false, "unlockSize": true, "uiScale": 1.0, "aspect_ratio_new": {"w": 16.0, "h": 9.0, "forceLandscape": false}, "autoFullscreen": false, "fullscreenBlockMinimization": true, "readRequestPack": false, "extraAssetBundlePaths": ["localized_data/umamusumelocalify"], "replaceFont": true, "customFontPath": "assets/bundledassets/umamusumelocalify/fonts/hanyi.otf", "customFontSizeOffset": -1, "customFontStyle": 0, "customFontLinespacing": 0.6, "replaceAssets": true, "dumpSpriteTexture": false, "dumpRuntimeTexture": false, "assetLoadLog": false, "live": {"free_camera": false, "mouseSpeed": 100.0, "force_changeVisibility_false": false, "moveStep": 0.1, "enableLiveDofController": false, "close_all_blur": false, "setLiveFovAsGlobal": false, "followUmaSmoothCamera": {"enable": false, "lookatStep": 0.01, "positionStep": 0.001}}, "homeSettings": {"free_camera": false}, "race_camera": {"free_camera": false, "moveStep": 5, "defaultFOV": 40, "freecam_lookat_target": false, "freecam_follow_target": false, "follow_offset": {"distance": 4, "x": 0, "y": 1, "z": 0}}, "cutin_first_person": false, "externalPlugin": {"hotkey": "u", "path": "legend_g_plugin.exe", "openExternalPluginOnLoad": true}, "autoChangeLineBreakMode": true, "resolution_start": [-1, -1], "httpServerPort": 43215, "dicts": ["localized_data/event.json", "localized_data/LIVE.json", "localized_data/hash_entries.json"], "static_dict": "localized_data/static.json", "no_static_dict_cache": true, "stories_path": "localized_data/stories", "text_data_dict": "localized_data/text_data.json", "character_system_text_dict": "localized_data/character_system_text.json", "race_jikkyo_comment_dict": "localized_data/race_jikkyo_comment.json", "race_jikkyo_message_dict": "localized_data/race_jikkyo_message.json", "autoUpdate": {"source": "github", "path": "http://uma.chinosk6.cn/api/get_localify_latest_releases"}, "enableBuiltinAutoUpdate": false, "replaceHomeStandChar": {"enable": false, "data": [{"origCharId": 1046, "newChrId": 2001, "newClothId": 9}]}, "replaceGlobalChar": {"enable": false, "replaceUniversal": true, "data": []}, "loadDll": [], "raceInfoTab": {"enableRaceInfoTab": false, "raceInfoTabAttachToGame": false}, "customPath": {"enableCustomPersistentDataPath": false, "customPersistentDataPath": ""}, "uploadGachaHistory": false, "enableEventHelper": false}