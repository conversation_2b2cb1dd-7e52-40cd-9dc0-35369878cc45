# Build Instructions for UmaCharReplacer

This document provides detailed instructions for building UmaCharReplacer from source.

## Prerequisites

### Required Software

1. **Visual Studio 2019 or later** with C++ support
   - Make sure to install the "Desktop development with C++" workload
   - Include the Windows 10/11 SDK (latest version recommended)
   - Include MSVC v143 compiler toolset

2. **vcpkg Package Manager**
   - Download and install vcpkg from: https://github.com/Microsoft/vcpkg
   - Follow the vcpkg installation guide to set it up properly
   - Integrate vcpkg with Visual Studio: `vcpkg integrate install`

3. **Git** (for cloning dependencies)

### System Requirements

- Windows 10 or later (x64)
- At least 4GB of free disk space for build tools and dependencies
- Administrator privileges (for some build operations)

## Building with Visual Studio

### Step 1: Install Dependencies

Open a command prompt in the project root directory and run:

```cmd
vcpkg install minhook:x64-windows
vcpkg install nlohmann-json:x64-windows
```

### Step 2: Open the Solution

1. Open `UmaCharReplacer.sln` in Visual Studio
2. Ensure the solution platform is set to **x64**
3. Choose your build configuration:
   - **Debug**: For development and debugging
   - **Release**: For production use (recommended)

### Step 3: Build the Project

1. Right-click on the solution in Solution Explorer
2. Select "Build Solution" or press `Ctrl+Shift+B`
3. Wait for the build to complete

The output will be generated in:
- Debug: `bin\x64\Debug\version.dll`
- Release: `bin\x64\Release\version.dll`

## Building with CMake

### Step 1: Install Dependencies

```cmd
vcpkg install minhook:x64-windows
vcpkg install nlohmann-json:x64-windows
```

### Step 2: Configure and Build

```cmd
mkdir build
cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=[path-to-vcpkg]/scripts/buildsystems/vcpkg.cmake -A x64
cmake --build . --config Release
```

Replace `[path-to-vcpkg]` with the actual path to your vcpkg installation.

## Build Output

After a successful build, you will have:

- `version.dll` - The main DLL to be loaded by Hachimi
- `hachimi/ucrconfig.json` - Configuration file for character replacements

## Installation

**Prerequisites: Hachimi must be installed and working**

1. **Copy the built DLL**:
   - Copy the newly built `version.dll` to your game directory
   - Rename it to `UmaCharReplacer.dll` (or any name you prefer)

2. **Configure Hachimi to load the DLL**:
   - Edit Hachimi's `config.json` file in your game directory
   - Add the DLL to the `load_libraries` array in the `windows` section:
   ```json
   {
     "windows": {
       "load_libraries": ["UmaCharReplacer.dll"]
     }
   }
   ```

3. **Configure character replacements**:
   - Launch the game once to create `hachimi/ucrconfig.json`
   - Edit `hachimi/ucrconfig.json` to specify your desired character replacements
   - See the main README.md for configuration details

## Troubleshooting

### Common Build Issues

**Error: "Cannot find vcpkg.cmake"**
- Ensure vcpkg is properly installed and integrated
- Check that the CMAKE_TOOLCHAIN_FILE path is correct

**Error: "minhook not found"**
- Make sure you installed the x64-windows version: `vcpkg install minhook:x64-windows`
- Verify vcpkg integration: `vcpkg integrate install`

**Error: "SAFESEH errors"**
- This is expected and handled by the build configuration
- The `/SAFESEH:NO` flag is automatically applied

**Error: "Cannot load version_orig.dll"**
- This error occurs at runtime, not build time
- Make sure you renamed the original version.dll to version_orig.dll

### Build Performance

For faster builds:
- Use the Release configuration for final builds
- Enable parallel compilation (already configured in the project)
- Use an SSD for the build directory

### Clean Build

If you encounter issues, try a clean build:

**Visual Studio:**
1. Right-click solution → "Clean Solution"
2. Right-click solution → "Rebuild Solution"

**CMake:**
```cmd
cd build
cmake --build . --target clean
cmake --build . --config Release
```

## Development Notes

### Code Style

- Use C++20 features where appropriate
- Follow the existing naming conventions
- Add logging for important operations
- Handle errors gracefully

### Adding New Features

1. Update the configuration schema in `config.hpp`
2. Implement the feature in the appropriate module
3. Add logging and error handling
4. Update documentation

### Debugging

For debugging the DLL:
1. Build in Debug configuration
2. Attach Visual Studio debugger to umamusume.exe
3. Set breakpoints in your code
4. Use the debug console for runtime information

## Important Notes

⚠️ **Function Address Resolution**: The current implementation includes placeholder code for function address resolution. To make this tool fully functional, you need to:

1. Reverse engineer the target game to find the correct function addresses
2. Implement pattern scanning or use known offsets for your game version
3. Update the `resolve_function_addresses()` function in `character_replacer.cpp`

This requires advanced reverse engineering skills and is specific to each game version.

⚠️ **Legal Disclaimer**: This tool is for educational purposes only. Use at your own risk and ensure compliance with the game's terms of service.

## Support

If you encounter build issues:
1. Check that all prerequisites are properly installed
2. Verify your vcpkg installation and integration
3. Try a clean build
4. Check the project's issue tracker for known problems
