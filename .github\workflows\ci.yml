name: ci
on: 
  workflow_dispatch:
  push: 
    paths:
      - '**.lua'
      - '**.cpp'
      - '**.c'
      - '**.hpp'
      - '**.h'
      - '**.yml'
      - '.gitmodules'
      - '.gitignore'
      - '.gitattributes'
      - 'conanfile.txt'
      - 'deps/**'
      - 'src/**'
      - 'utils/**'
  pull_request:
      paths:
      - '**.lua'
      - '**.cpp'
      - '**.c'
      - '**.hpp'
      - '**.h'
      - '**.yml'
      - '.gitmodules'
      - '.gitignore'
      - '.gitattributes'
      - 'conanfile.txt'
      - 'deps/**'
      - 'src/**'
      - 'utils/**'
jobs:
  build:
    runs-on: windows-2022

    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: install-dependencies
        run: |
          pip3 install --upgrade conan==1.59.0 requests
          conan profile new default --detect
          conan config init
      - name: configure-msbuild-env
        uses: microsoft/setup-msbuild@v1.0.2
      - name: build
        run: |
          ./generate.bat
          cd build
          msbuild umamusume-localify.sln -m -p:Configuration=Release
      - name: prepare-package
        run: |
          mkdir package
          cp build/bin/x64/Release/version.dll package/tlg.dll
          cp -r resources/schinese/localized_data package/localized_data
          cp resources/config.json package/config.json
          mkdir package/localized_data/config_schema
          cp resources/config.schema.json package/localized_data/config_schema/config.schema.json
          cp resources/config_en.schema.json package/localized_data/config_schema/config_en.schema.json
          cp resources/config_zh_tw.schema.json package/localized_data/config_schema/config_zh_tw.schema.json
          cp resources/config_ja.schema.json package/localized_data/config_schema/config_ja.schema.json
          cp resources/text_data_info.json package/localized_data/config_schema/text_data_info.json
          cp resources/text_data_info_i18n_zh.json package/localized_data/config_schema/text_data_info_i18n_zh.json
          cp resources/text_data_info_i18n_zh_tw.json package/localized_data/config_schema/text_data_info_i18n_zh_tw.json
          cp resources/text_data_info_i18n_ja.json package/localized_data/config_schema/text_data_info_i18n_ja.json
          cp resources/legend_g_plugin.exe package/legend_g_plugin.exe.autoupdate
          cp resources/loader.dll package/loader.dll
          cp resources/tlg_starter.exe package/tlg_starter.exe
          cp -r resources/eventHelper package/localized_data/eventHelper_examples
      - uses: actions/upload-artifact@v4
        with:
          name: trainers-legend-g
          path: package
      - uses: actions/upload-artifact@v4
        with:
          name: trainers-legend-g-dll
          path: package/tlg.dll
