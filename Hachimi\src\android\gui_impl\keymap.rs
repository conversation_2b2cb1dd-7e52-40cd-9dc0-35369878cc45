use egui::Key;
use jni::sys::jint;

pub const KEYCODE_DPAD_UP: jint = 19;
pub const KEYCODE_DPAD_DOWN: jint = 20;
pub const KEYCODE_DPAD_LEFT: jint = 21;
pub const KEYCODE_DPAD_RIGHT: jint = 22;
pub const KEYCODE_ESCAPE: jint = 111;
pub const KEYCODE_TAB: jint = 61;
pub const KEYCODE_DEL: jint = 67;
pub const KEYCODE_ENTER: jint = 66;
pub const KEYCODE_SPACE: jint = 62;
pub const KEYCODE_INSERT: jint = 124;
pub const KEYCODE_FORWARD_DEL: jint = 112;
pub const KEYCODE_MOVE_HOME: jint = 122;
pub const KEYCODE_MOVE_END: jint = 123;
pub const KEYCODE_PAGE_UP: jint = 92;
pub const KEYCODE_PAGE_DOWN: jint = 93;
pub const KEY<PERSON><PERSON>_COPY: jint = 278;
pub const KEYCODE_CUT: jint = 277;
pub const KEYCODE_PASTE: jint = 279;
pub const KEYCODE_SEMICOLON: jint = 74;
pub const KEYCODE_COMMA: jint = 55;
pub const KEYCODE_BACKSLASH: jint = 73;
pub const KEYCODE_SLASH: jint = 76;
pub const KEYCODE_LEFT_BRACKET: jint = 71;
pub const KEYCODE_RIGHT_BRACKET: jint = 72;
pub const KEYCODE_GRAVE: jint = 68;
pub const KEYCODE_MINUS: jint = 69;
pub const KEYCODE_PERIOD: jint = 56;
pub const KEYCODE_PLUS: jint = 81;
pub const KEYCODE_EQUALS: jint = 70;
pub const KEYCODE_0: jint = 7;
pub const KEYCODE_1: jint = 8;
pub const KEYCODE_2: jint = 9;
pub const KEYCODE_3: jint = 10;
pub const KEYCODE_4: jint = 11;
pub const KEYCODE_5: jint = 12;
pub const KEYCODE_6: jint = 13;
pub const KEYCODE_7: jint = 14;
pub const KEYCODE_8: jint = 15;
pub const KEYCODE_9: jint = 16;
pub const KEYCODE_A: jint = 29;
pub const KEYCODE_B: jint = 30;
pub const KEYCODE_C: jint = 31;
pub const KEYCODE_D: jint = 32;
pub const KEYCODE_E: jint = 33;
pub const KEYCODE_F: jint = 34;
pub const KEYCODE_G: jint = 35;
pub const KEYCODE_H: jint = 36;
pub const KEYCODE_I: jint = 37;
pub const KEYCODE_J: jint = 38;
pub const KEYCODE_K: jint = 39;
pub const KEYCODE_L: jint = 40;
pub const KEYCODE_M: jint = 41;
pub const KEYCODE_N: jint = 42;
pub const KEYCODE_O: jint = 43;
pub const KEYCODE_P: jint = 44;
pub const KEYCODE_Q: jint = 45;
pub const KEYCODE_R: jint = 46;
pub const KEYCODE_S: jint = 47;
pub const KEYCODE_T: jint = 48;
pub const KEYCODE_U: jint = 49;
pub const KEYCODE_V: jint = 50;
pub const KEYCODE_W: jint = 51;
pub const KEYCODE_X: jint = 52;
pub const KEYCODE_Y: jint = 53;
pub const KEYCODE_Z: jint = 54;
pub const KEYCODE_F1: jint = 131;
pub const KEYCODE_F2: jint = 132;
pub const KEYCODE_F3: jint = 133;
pub const KEYCODE_F4: jint = 134;
pub const KEYCODE_F5: jint = 135;
pub const KEYCODE_F6: jint = 136;
pub const KEYCODE_F7: jint = 137;
pub const KEYCODE_F8: jint = 138;
pub const KEYCODE_F9: jint = 139;
pub const KEYCODE_F10: jint = 140;
pub const KEYCODE_F11: jint = 141;
pub const KEYCODE_F12: jint = 142;

pub const KEYCODE_VOLUME_UP: jint = 24;
pub const KEYCODE_VOLUME_DOWN: jint = 25;

pub fn get_key(key_code: jint) -> Option<Key> {
    match key_code {
        KEYCODE_DPAD_UP => Some(Key::ArrowUp),
        KEYCODE_DPAD_DOWN => Some(Key::ArrowDown),
        KEYCODE_DPAD_LEFT => Some(Key::ArrowLeft),
        KEYCODE_DPAD_RIGHT => Some(Key::ArrowRight),
        KEYCODE_ESCAPE => Some(Key::Escape),
        KEYCODE_TAB => Some(Key::Tab),
        KEYCODE_DEL => Some(Key::Backspace),
        KEYCODE_ENTER => Some(Key::Enter),
        KEYCODE_SPACE => Some(Key::Space),
        KEYCODE_INSERT => Some(Key::Insert),
        KEYCODE_FORWARD_DEL => Some(Key::Delete),
        KEYCODE_MOVE_HOME => Some(Key::Home),
        KEYCODE_MOVE_END => Some(Key::End),
        KEYCODE_PAGE_UP => Some(Key::PageUp),
        KEYCODE_PAGE_DOWN => Some(Key::PageDown),
        KEYCODE_COPY => Some(Key::Copy),
        KEYCODE_CUT => Some(Key::Cut),
        KEYCODE_PASTE => Some(Key::Paste),
        KEYCODE_SEMICOLON => Some(Key::Semicolon),
        KEYCODE_COMMA => Some(Key::Comma),
        KEYCODE_BACKSLASH => Some(Key::Backslash),
        KEYCODE_SLASH => Some(Key::Slash),
        KEYCODE_LEFT_BRACKET => Some(Key::OpenBracket),
        KEYCODE_RIGHT_BRACKET => Some(Key::CloseBracket),
        KEYCODE_GRAVE => Some(Key::Backtick),
        KEYCODE_MINUS => Some(Key::Minus),
        KEYCODE_PERIOD => Some(Key::Period),
        KEYCODE_PLUS => Some(Key::Plus),
        KEYCODE_EQUALS => Some(Key::Equals),
        KEYCODE_0 => Some(Key::Num0),
        KEYCODE_1 => Some(Key::Num1),
        KEYCODE_2 => Some(Key::Num2),
        KEYCODE_3 => Some(Key::Num3),
        KEYCODE_4 => Some(Key::Num4),
        KEYCODE_5 => Some(Key::Num5),
        KEYCODE_6 => Some(Key::Num6),
        KEYCODE_7 => Some(Key::Num7),
        KEYCODE_8 => Some(Key::Num8),
        KEYCODE_9 => Some(Key::Num9),
        KEYCODE_A => Some(Key::A),
        KEYCODE_B => Some(Key::B),
        KEYCODE_C => Some(Key::C),
        KEYCODE_D => Some(Key::D),
        KEYCODE_E => Some(Key::E),
        KEYCODE_F => Some(Key::F),
        KEYCODE_G => Some(Key::G),
        KEYCODE_H => Some(Key::H),
        KEYCODE_I => Some(Key::I),
        KEYCODE_J => Some(Key::J),
        KEYCODE_K => Some(Key::K),
        KEYCODE_L => Some(Key::L),
        KEYCODE_M => Some(Key::M),
        KEYCODE_N => Some(Key::N),
        KEYCODE_O => Some(Key::O),
        KEYCODE_P => Some(Key::P),
        KEYCODE_Q => Some(Key::Q),
        KEYCODE_R => Some(Key::R),
        KEYCODE_S => Some(Key::S),
        KEYCODE_T => Some(Key::T),
        KEYCODE_U => Some(Key::U),
        KEYCODE_V => Some(Key::V),
        KEYCODE_W => Some(Key::W),
        KEYCODE_X => Some(Key::X),
        KEYCODE_Y => Some(Key::Y),
        KEYCODE_Z => Some(Key::Z),
        KEYCODE_F1 => Some(Key::F1),
        KEYCODE_F2 => Some(Key::F2),
        KEYCODE_F3 => Some(Key::F3),
        KEYCODE_F4 => Some(Key::F4),
        KEYCODE_F5 => Some(Key::F5),
        KEYCODE_F6 => Some(Key::F6),
        KEYCODE_F7 => Some(Key::F7),
        KEYCODE_F8 => Some(Key::F8),
        KEYCODE_F9 => Some(Key::F9),
        KEYCODE_F10 => Some(Key::F10),
        KEYCODE_F11 => Some(Key::F11),
        KEYCODE_F12 => Some(Key::F12),
        _ => None
    }
}