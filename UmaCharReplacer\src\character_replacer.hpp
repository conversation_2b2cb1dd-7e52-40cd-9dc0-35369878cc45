#pragma once

#include "common.hpp"

namespace character_replacer {
    // Initialize character replacement hooks
    bool initialize();
    
    // Uninitialize and clean up hooks
    void uninitialize();
    
    // Core replacement logic
    bool replace_character_controller(int* charaId, int* dressId, int* headId, UmaControllerType controllerType);
    bool replace_character_controller(int* cardId, int* charaId, int* dressId, int* headId, UmaControllerType controllerType);
    
    // Utility functions
    int get_head_id_from_dress_id(int dressId);
    bool is_replacement_context(UmaControllerType controllerType);
}
