#pragma once

#include "common.hpp"

namespace il2cpp_helper {
    // Set GameAssembly handle (called from LoadLibraryW hook)
    void set_game_assembly_handle(HMODULE handle);

    // Initialize IL2CPP function pointers
    bool initialize();
    
    // Utility functions for IL2CPP interaction
    void* get_class(const char* assembly_name, const char* namespace_name, const char* class_name);
    FieldInfo* get_field(void* klass, const char* field_name);
    size_t get_field_offset(FieldInfo* field);
    
    // Template functions for reading/writing fields
    template<typename T>
    T read_field(void* object, FieldInfo* field) {
        if (!object || !field) return T{};
        auto offset = get_field_offset(field);
        return *reinterpret_cast<T*>(static_cast<char*>(object) + offset);
    }

    template<typename T>
    void write_field(void* object, FieldInfo* field, const T& value) {
        if (!object || !field) return;
        auto offset = get_field_offset(field);
        *reinterpret_cast<T*>(static_cast<char*>(object) + offset) = value;
    }
    
    // Specific field accessors for character data
    struct CharacterFields {
        FieldInfo* charaId = nullptr;
        FieldInfo* dressId = nullptr;
        FieldInfo* headModelSubId = nullptr;
        FieldInfo* motionDressId = nullptr;
        FieldInfo* cardId = nullptr;
    };
    
    extern CharacterFields g_character_fields;
    
    // Initialize character-related fields
    bool initialize_character_fields();
}
