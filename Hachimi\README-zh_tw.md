<img align="left" width="80" height="80" src="assets/icon.png">

# Hachi<PERSON>

[English](README.md) | [简体中文](README-zh_cn.md) | 繁體中文

[![Discord 伺服器](https://dcbadge.limes.pink/api/server/https://discord.gg/BVEt5FcxEn)](https://discord.gg/BVEt5FcxEn)

馬姑娘:媠達比的遊戲強化與翻譯模組

<img height="400" src="assets/screenshot.jpg">

# 功能特色
- **高品質翻譯支援：** Hachimi 搭載了進階的翻譯處理功能，讓翻譯結果更自然流暢（支援複數型、序數詞等），並避免破壞遊戲介面。它也支援翻譯絕大多數的遊戲內容，無需手動修改資源檔！

    支援的元件包括：
    - UI 介面文字
    - master.mdb（技能名稱、技能描述等等）
    - 角色劇情
    - 主線劇情／主畫面對話
    - 歌詞
    - Texture替換
    - Sprite atlas替換

    此外，Hachimi 並不限於支援單一語言，它設計之初就以「多語系完全可設定」為目標。

- **簡單安裝：** 只要放入就能使用，所有設定都能在遊戲內完成，無需額外應用程式。
- **翻譯自動更新：** 內建翻譯更新器可在遊戲中持續遊玩同時更新，更新完成後自動重新載入，完全不需重啟遊戲！
- **內建圖形介面（GUI）：** 附帶設定編輯器，可在遊戲內直接調整各種選項，無需退出。
- **畫質調整選項：** 可依照裝置效能調整畫面設定，例如解除 FPS 限制、縮放解析度等功能。
- **跨平台支援：** 可用於多平台，目前支援 Windows 與 Android。

# 安裝方式
請參閱 [快速開始指南](https://hachimi.leadrdrk.com/zh-tw/docs/hachimi/getting-started.html) 頁面。

# 特別感謝
以下這些專案是 Hachimi 開發的重要基礎，沒有他們的貢獻就沒有今天的 Hachimi：

- [Trainers' Legend G](https://github.com/MinamiChiwa/Trainers-Legend-G)
- [umamusume-localify-android](https://github.com/Kimjio/umamusume-localify-android)
- [umamusume-localify](https://github.com/GEEKiDoS/umamusume-localify)
- [Carotenify](https://github.com/KevinVG207/Uma-Carotenify)
- [umamusu-translate](https://github.com/noccu/umamusu-translate)
- [frida-il2cpp-bridge](https://github.com/vfsfitvnm/frida-il2cpp-bridge)

# 授權條款
[GNU GPLv3](LICENSE)