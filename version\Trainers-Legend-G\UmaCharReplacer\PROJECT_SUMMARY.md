# UmaCharReplacer Project Summary

## Project Overview

UmaCharReplacer is a character model replacement tool for Umamusume: Pretty Derby, inspired by the CarrotJuicer hooking approach and Trainers-Legend-G's model replacement functionality. This project demonstrates how to create a game modification tool using DLL proxy injection and function hooking techniques.

## Architecture and Design

### Core Components

1. **DLL Proxy System** (`proxy.cpp`, `version.asm`, `version.def`)
   - Implements version.dll proxy injection
   - Forwards all version.dll functions to original DLL
   - Provides entry point for code injection

2. **Hooking Infrastructure** (`hook.cpp`)
   - MinHook integration for function interception
   - LoadLibraryW hook for game initialization detection
   - Hook management and cleanup

3. **IL2CPP Integration** (`il2cpp_helper.cpp`)
   - IL2CPP function pointer resolution
   - Class and field access utilities
   - Template-based field read/write operations

4. **Character Replacement System** (`character_replacer.cpp`)
   - Core replacement logic ported from Trainers-Legend-G
   - Support for home stand and global character replacement
   - Hook points for character loading functions

5. **Configuration System** (`config.cpp`)
   - JSON-based configuration with nlohmann/json
   - Runtime configuration reloading
   - Comprehensive validation and error handling

### Design Patterns Used

- **Proxy Pattern**: DLL proxy for non-intrusive injection
- **Observer Pattern**: Hook callbacks for game events
- **Template Pattern**: Generic IL2CPP field access
- **Strategy Pattern**: Different replacement strategies for different contexts

## Technical Implementation

### Hooking Methodology (Inspired by CarrotJuicer)

1. **DLL Proxy Injection**:
   - Replace system version.dll with custom DLL
   - Forward all API calls to original DLL
   - Gain execution context within target process

2. **Initialization Detection**:
   - Hook LoadLibraryW to detect cri_ware_unity.dll loading
   - This indicates game is ready for modification
   - Initialize character replacement hooks at this point

3. **Function Interception**:
   - Use MinHook for robust function hooking
   - Target character building and loading functions
   - Modify parameters before calling original functions

### Character Replacement Logic (From Trainers-Legend-G)

1. **Replacement Contexts**:
   - Home Stand: Character on home screen
   - Global: Characters in races, training, stories
   - Mini Characters: Small character models

2. **Replacement Process**:
   - Intercept character loading functions
   - Check if character ID should be replaced
   - Modify character ID, dress ID, and head ID
   - Update related fields (card ID, motion dress ID)

3. **Configuration-Driven**:
   - JSON configuration specifies replacements
   - Runtime reloading of configuration
   - Granular control over replacement contexts

## Project Structure

```
UmaCharReplacer/
├── src/                          # Source code
│   ├── common.hpp               # Common definitions and types
│   ├── config.hpp/.cpp          # Configuration system
│   ├── hook.cpp                 # Main hooking logic
│   ├── dllmain.cpp             # DLL entry point
│   ├── proxy.cpp               # DLL proxy implementation
│   ├── il2cpp_helper.hpp/.cpp  # IL2CPP integration
│   ├── character_replacer.hpp/.cpp # Character replacement logic
│   ├── version.asm             # Assembly trampolines
│   └── version.def             # DLL export definitions
├── config.json                 # Default configuration
├── CMakeLists.txt              # CMake build configuration
├── UmaCharReplacer.sln/.vcxproj # Visual Studio project files
├── vcpkg.json                  # Dependency management
├── README.md                   # Main documentation
├── BUILD.md                    # Build instructions
├── CONFIGURATION.md            # Configuration guide
├── TESTING.md                  # Testing procedures
└── LICENSE                     # MIT license
```

## Key Features Implemented

### ✅ Completed Features

1. **Complete Build System**:
   - CMake and Visual Studio project files
   - vcpkg dependency management
   - Proper x64 architecture support

2. **DLL Proxy Infrastructure**:
   - Full version.dll proxy implementation
   - Assembly trampolines for all exports
   - Proper function forwarding

3. **Configuration System**:
   - JSON-based configuration with validation
   - Support for home stand and global replacements
   - Runtime configuration reloading

4. **Hooking Framework**:
   - MinHook integration
   - Game initialization detection
   - Hook management and cleanup

5. **IL2CPP Helper System**:
   - Function pointer resolution framework
   - Template-based field access
   - Character field definitions

6. **Character Replacement Logic**:
   - Complete replacement algorithm
   - Support for different controller types
   - Head ID calculation and card ID updates

7. **Comprehensive Documentation**:
   - Build instructions with troubleshooting
   - Configuration guide with examples
   - Testing procedures and validation

### ❌ Requires Implementation

1. **Function Address Resolution**:
   - Pattern scanning for game functions
   - Version-specific address management
   - Robust function finding algorithms

2. **Game-Specific Integration**:
   - Actual hook point implementation
   - Game version compatibility
   - Testing with real game environment

## Technical Challenges Addressed

### 1. DLL Proxy Implementation
- **Challenge**: Create transparent proxy for version.dll
- **Solution**: Assembly trampolines with proper export forwarding
- **Result**: Non-intrusive injection method

### 2. Game Initialization Detection
- **Challenge**: Determine when game is ready for modification
- **Solution**: Hook LoadLibraryW and detect cri_ware_unity.dll
- **Result**: Reliable initialization timing

### 3. IL2CPP Integration
- **Challenge**: Access Unity IL2CPP runtime from native code
- **Solution**: Dynamic function pointer resolution and template helpers
- **Result**: Type-safe field access system

### 4. Configuration Management
- **Challenge**: Flexible, user-friendly configuration system
- **Solution**: JSON with comprehensive validation and error handling
- **Result**: Easy-to-use configuration with good error reporting

## Learning Outcomes

This project demonstrates:

1. **Advanced Windows Programming**:
   - DLL injection techniques
   - Function hooking with MinHook
   - Assembly language integration

2. **Game Modification Techniques**:
   - Non-intrusive modification methods
   - Runtime function interception
   - Memory manipulation safety

3. **Software Architecture**:
   - Modular design patterns
   - Configuration-driven behavior
   - Error handling and logging

4. **Build System Management**:
   - Cross-platform build configuration
   - Dependency management with vcpkg
   - Multiple build system support

## Future Enhancements

To make this tool fully functional:

1. **Pattern Scanning Implementation**:
   - Robust function signature scanning
   - Multi-version compatibility
   - Automatic address resolution

2. **Enhanced Error Handling**:
   - Better error recovery mechanisms
   - More detailed error reporting
   - Graceful degradation

3. **Additional Hook Points**:
   - More character loading scenarios
   - Animation and effect replacements
   - Extended customization options

4. **User Interface**:
   - GUI configuration tool
   - Real-time replacement preview
   - Character ID database integration

## Educational Value

This project serves as an excellent educational resource for:

- **Game Modification Techniques**: Demonstrates professional-grade modding approaches
- **Windows System Programming**: Shows advanced DLL and hooking techniques
- **Software Architecture**: Illustrates modular, maintainable design patterns
- **Reverse Engineering**: Provides framework for game analysis and modification

## Conclusion

UmaCharReplacer successfully demonstrates how to create a sophisticated game modification tool by combining the CarrotJuicer hooking approach with Trainers-Legend-G's character replacement functionality. While the core framework is complete and functional, it requires reverse engineering expertise to implement the final game-specific integration.

The project provides a solid foundation for anyone interested in learning game modification techniques, Windows system programming, or software architecture for game tools. The comprehensive documentation and modular design make it an excellent starting point for similar projects or educational purposes.
