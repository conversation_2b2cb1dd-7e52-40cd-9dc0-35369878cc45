#pragma once

#include <Windows.h>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>
#include <filesystem>
#include <fstream>
#include <thread>
#include <mutex>
#include <iomanip>
#include <sstream>
#include <chrono>

// Common types and definitions
using CharacterReplacement = std::pair<int, int>; // <newCharId, newClothId>
using CharacterReplacementMap = std::unordered_map<int, CharacterReplacement>;

// Controller types for different game contexts
enum class UmaControllerType : int {
    HomeStand = 0x1919810,
    Race = 0x191981A,
    Training = 0x191981B,
    Story = 0x191981C,
    ORIG = 0x0
};

// Global variables
extern bool g_enable_home_char_replace;
extern bool g_enable_global_char_replace;
extern bool g_global_char_replace_Universal;
extern bool g_enable_console;
extern bool g_enable_logging;
extern bool g_hook_ready;

extern CharacterReplacementMap g_home_char_replace;
extern CharacterReplacementMap g_global_char_replace;
extern CharacterReplacementMap g_global_mini_char_replace;

// Utility functions
void create_debug_console();
void log_message(const std::string& message);
std::string get_current_time_string();

// IL2CPP related definitions
struct Il2CppString {
    void* klass;
    void* monitor;
    int length;
    wchar_t chars[1];
};

// IL2CPP structure definitions (from Trainers-Legend-G)
struct FieldInfo {
    const char* name;
    void* type;
    void* parent;
    int32_t offset;
    uint32_t token;
};

// Function pointer types for IL2CPP
using il2cpp_field_get_offset_t = size_t(*)(void* field);
using il2cpp_class_get_field_from_name_t = FieldInfo*(*)(void* klass, const char* name);
using il2cpp_domain_get_t = void*(*)();
using il2cpp_domain_assembly_open_t = void*(*)(void* domain, const char* name);
using il2cpp_assembly_get_image_t = void*(*)(void* assembly);
using il2cpp_class_from_name_t = void*(*)(void* image, const char* namespaze, const char* name);

// Global IL2CPP function pointers
extern il2cpp_field_get_offset_t il2cpp_field_get_offset;
extern il2cpp_class_get_field_from_name_t il2cpp_class_get_field_from_name;
extern il2cpp_domain_get_t il2cpp_domain_get;
extern il2cpp_domain_assembly_open_t il2cpp_domain_assembly_open;
extern il2cpp_assembly_get_image_t il2cpp_assembly_get_image;
extern il2cpp_class_from_name_t il2cpp_class_from_name;
