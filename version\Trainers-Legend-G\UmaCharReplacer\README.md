# UmaCharReplacer

A character model replacement tool for Umamusume: Pretty Derby, inspired by the CarrotJuicer hooking approach and Trainers-Legend-G's model replacement functionality.

## Features

- **Character Model Replacement**: Replace any character with another character and specific outfit
- **Home Stand Character Replacement**: Replace characters specifically on the home screen
- **Global Character Replacement**: Replace characters throughout the game (races, training, etc.)
- **Configuration-Based**: Easy-to-use JSON configuration file for specifying replacements
- **Non-Intrusive**: Uses DLL proxy injection method similar to CarrotJuicer

## How It Works

This tool uses a DLL proxy injection technique to hook into the game's character loading functions. When the game attempts to load a character model, the tool intercepts the call and replaces the character ID and cloth ID with the configured replacements.

The hooking approach is inspired by <PERSON><PERSON><PERSON><PERSON><PERSON>'s methodology:
- Uses `version.dll` proxy to inject into the game process
- Hooks `LoadLibraryW` to detect when the game is ready for modification
- Uses MinHook library for function hooking
- Intercepts IL2CPP character building functions

## Configuration

Edit the `hachimi/ucrconfig.json` file to specify your character replacements:

```json
{
    "replaceHomeStandChar": {
        "enable": true,
        "data": [
            {
                "origCharId": 1046,
                "newChrId": 2001,
                "newClothId": 200101
            }
        ]
    },
    "replaceGlobalChar": {
        "enable": true,
        "replaceUniversal": true,
        "data": [
            {
                "origCharId": 1001,
                "newChrId": 1046,
                "newClothId": 104601,
                "replaceMini": false
            }
        ]
    }
}
```

### Configuration Options

- **origCharId**: The original character ID to replace
- **newChrId**: The new character ID to use as replacement
- **newClothId**: The cloth/outfit ID for the replacement character
- **replaceMini**: Whether to also replace mini character models (global replacement only)
- **replaceUniversal**: Whether to replace universal outfits as well

## Installation

**Note: This tool requires Hachimi to be installed and working first.**

1. **Install Hachimi** (if not already installed)
   - Download and install Hachimi from the Trainers-Legend-G project
   - Ensure Hachimi is working with your game

2. **Build UmaCharReplacer** (see Build Instructions below)
   - Build the project to generate `version.dll`

3. **Configure Hachimi to load UmaCharReplacer**
   - Copy the generated `version.dll` to your game directory (rename it to `UmaCharReplacer.dll`)
   - Edit Hachimi's `config.json` file
   - Add the following to the `windows` section:
   ```json
   "load_libraries": ["UmaCharReplacer.dll"]
   ```

4. **Launch the game**
   - Start the game normally (Hachimi will automatically load UmaCharReplacer)
   - The `hachimi/ucrconfig.json` file will be automatically created

## Build Instructions

### Prerequisites

- Visual Studio 2019 or later with C++ support
- vcpkg package manager
- Windows 10 SDK

### Dependencies

The project uses the following dependencies managed by vcpkg:
- `minhook`: For function hooking
- `nlohmann-json`: For JSON configuration parsing

### Building

1. Clone this repository
2. Install dependencies using vcpkg:
   ```cmd
   vcpkg install minhook:x64-windows
   vcpkg install nlohmann-json:x64-windows
   ```
3. Open the solution in Visual Studio
4. Build the project in Release mode for x64 architecture

### Alternative Build with CMake

1. Ensure vcpkg is integrated with CMake
2. Run the following commands:
   ```cmd
   mkdir build
   cd build
   cmake .. -DCMAKE_TOOLCHAIN_FILE=[vcpkg root]/scripts/buildsystems/vcpkg.cmake
   cmake --build . --config Release
   ```

## Character and Cloth ID Reference

Character IDs and cloth IDs can be found in the game's database files or through community resources. Common character IDs include:
- 1001: Special Week
- 1002: Silence Suzuka
- 1003: Tokai Teio
- 1046: Smart Falcon
- 2001: Trainer (various outfits)

Cloth IDs typically follow the pattern: `[CharID][OutfitNumber]` (e.g., 104601 for Smart Falcon's racing outfit)

## ⚠️ Important Implementation Notice

**This project is currently a framework/template that requires additional implementation work to be fully functional:**

### What's Implemented
- ✅ Complete project structure and build system
- ✅ DLL proxy injection mechanism (CarrotJuicer approach)
- ✅ Configuration system for character replacements
- ✅ IL2CPP helper framework
- ✅ Character replacement logic structure
- ✅ Comprehensive documentation and build instructions

### What Needs Implementation
- ❌ **Function Address Resolution**: The core hook points need actual game function addresses
- ❌ **Pattern Scanning**: Robust method to find functions across game versions
- ❌ **Game-Specific Integration**: Adaptation to specific Umamusume versions

### To Complete This Tool
1. **Reverse Engineer Target Functions**: Find addresses for character loading functions
2. **Implement Pattern Scanning**: Create version-independent function finding
3. **Test and Validate**: Ensure compatibility with target game version

This serves as a complete foundation that demonstrates the CarrotJuicer hooking approach applied to character model replacement, but requires reverse engineering expertise to complete.

## Safety and Disclaimer

This tool is for educational and research purposes only. Use at your own risk. The developers are not responsible for any issues that may arise from using this tool, including but not limited to:
- Game crashes or instability
- Account suspension or bans
- Data corruption
- Violation of terms of service

Always backup your game saves before using any modification tools. Ensure compliance with the game's terms of service and applicable laws.

## Documentation

- **[BUILD.md](BUILD.md)**: Detailed build instructions and troubleshooting
- **[CONFIGURATION.md](CONFIGURATION.md)**: Complete configuration guide with examples
- **[TESTING.md](TESTING.md)**: Testing procedures and validation steps

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **CarrotJuicer**: For the DLL proxy injection methodology and hooking approach
- **Trainers-Legend-G**: For the character replacement implementation and configuration structure
- **MinHook**: For the excellent function hooking library
- **nlohmann/json**: For JSON configuration parsing
