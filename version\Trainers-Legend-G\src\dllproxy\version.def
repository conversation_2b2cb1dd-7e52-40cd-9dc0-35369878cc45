LIBRARY version.dll
EXPORTS
	GetFileVersionInfoA=GetFileVersionInfoA_EXPORT
	GetFileVersionInfoByHandle=GetFileVersionInfoByHandle_EXPORT
	GetFileVersionInfoExA=GetFileVersionInfoExA_EXPORT
	GetFileVersionInfoExW=GetFileVersionInfoExW_EXPORT
	GetFileVersionInfoSizeA=GetFileVersionInfoSizeA_EXPORT
	GetFileVersionInfoSizeExA=GetFileVersionInfoSizeExA_EXPORT
	GetFileVersionInfoSizeExW=GetFileVersionInfoSizeExW_EXPORT
	GetFileVersionInfoSizeW=GetFileVersionInfoSizeW_EXPORT
	GetFileVersionInfoW=GetFileVersionInfoW_EXPORT
	VerFindFileA=VerFindFileA_EXPORT
	VerFindFileW=VerFindFileW_EXPORT
	VerInstallFileA=VerInstallFileA_EXPORT
	VerInstallFileW=VerInstallFileW_EXPORT
	VerLanguageNameA=VerLanguageNameA_EXPORT
	VerLanguageNameW=VerLanguageNameW_EXPORT
	VerQueryValueA=VerQueryValueA_EXPORT
	VerQueryValueW=VerQueryValueW_EXPORT
