.code

; Export trampolines for version.dll functions
; These will jump to the original functions loaded by proxy.cpp

EXTERN GetFileVersionInfoA_Original:QWORD
EXTERN GetFileVersionInfoByHandle_Original:QWORD
EXTERN GetFileVersionInfoExA_Original:QWORD
EXTERN GetFileVersionInfoExW_Original:QWORD
EXTERN GetFileVersionInfoSizeA_Original:QWORD
EXTERN GetFileVersionInfoSizeExA_Original:QWORD
EXTERN GetFileVersionInfoSizeExW_Original:QWORD
EXTERN GetFileVersionInfoSizeW_Original:QWORD
EXTERN GetFileVersionInfoW_Original:QWORD
EXTERN VerFindFileA_Original:QWORD
EXTERN VerFindFileW_Original:QWORD
EXTERN VerInstallFileA_Original:QWORD
EXTERN VerInstallFileW_Original:QWORD
EXTERN VerLanguageNameA_Original:QWORD
EXTERN VerLanguageNameW_Original:QWORD
EXTERN VerQueryValueA_Original:QWORD
EXTERN VerQueryValueW_Original:QWORD

GetFileVersionInfoA_Trampoline PROC
    jmp GetFileVersionInfoA_Original
GetFileVersionInfoA_Trampoline ENDP

GetFileVersionInfoByHandle_Trampoline PROC
    jmp GetFileVersionInfoByHandle_Original
GetFileVersionInfoByHandle_Trampoline ENDP

GetFileVersionInfoExA_Trampoline PROC
    jmp GetFileVersionInfoExA_Original
GetFileVersionInfoExA_Trampoline ENDP

GetFileVersionInfoExW_Trampoline PROC
    jmp GetFileVersionInfoExW_Original
GetFileVersionInfoExW_Trampoline ENDP

GetFileVersionInfoSizeA_Trampoline PROC
    jmp GetFileVersionInfoSizeA_Original
GetFileVersionInfoSizeA_Trampoline ENDP

GetFileVersionInfoSizeExA_Trampoline PROC
    jmp GetFileVersionInfoSizeExA_Original
GetFileVersionInfoSizeExA_Trampoline ENDP

GetFileVersionInfoSizeExW_Trampoline PROC
    jmp GetFileVersionInfoSizeExW_Original
GetFileVersionInfoSizeExW_Trampoline ENDP

GetFileVersionInfoSizeW_Trampoline PROC
    jmp GetFileVersionInfoSizeW_Original
GetFileVersionInfoSizeW_Trampoline ENDP

GetFileVersionInfoW_Trampoline PROC
    jmp GetFileVersionInfoW_Original
GetFileVersionInfoW_Trampoline ENDP

VerFindFileA_Trampoline PROC
    jmp VerFindFileA_Original
VerFindFileA_Trampoline ENDP

VerFindFileW_Trampoline PROC
    jmp VerFindFileW_Original
VerFindFileW_Trampoline ENDP

VerInstallFileA_Trampoline PROC
    jmp VerInstallFileA_Original
VerInstallFileA_Trampoline ENDP

VerInstallFileW_Trampoline PROC
    jmp VerInstallFileW_Original
VerInstallFileW_Trampoline ENDP

VerLanguageNameA_Trampoline PROC
    jmp VerLanguageNameA_Original
VerLanguageNameA_Trampoline ENDP

VerLanguageNameW_Trampoline PROC
    jmp VerLanguageNameW_Original
VerLanguageNameW_Trampoline ENDP

VerQueryValueA_Trampoline PROC
    jmp VerQueryValueA_Original
VerQueryValueA_Trampoline ENDP

VerQueryValueW_Trampoline PROC
    jmp VerQueryValueW_Original
VerQueryValueW_Trampoline ENDP

END
