#pragma once

#include "common.hpp"
#include <nlohmann/json.hpp>

namespace config {
    struct CharacterReplaceData {
        int origCharId;
        int newChrId;
        int newClothId;
        bool replaceMini = false;
    };

    struct HomeStandCharConfig {
        bool enable = false;
        std::vector<CharacterReplaceData> data;
    };

    struct GlobalCharConfig {
        bool enable = false;
        bool replaceUniversal = true;
        std::vector<CharacterReplaceData> data;
    };

    struct Config {
        HomeStandCharConfig replaceHomeStandChar;
        GlobalCharConfig replaceGlobalChar;
        bool enableConsole = false;
        bool enableLogging = true;
    };

    // Global config instance
    extern Config g_config;

    // Functions
    bool load_config();
    bool create_default_config();
    void reload_config();
    const Config& get_config();
    
    // JSON serialization
    void to_json(nlohmann::json& j, const CharacterReplaceData& data);
    void from_json(const nlohmann::json& j, CharacterReplaceData& data);
    void to_json(nlohmann::json& j, const HomeStandCharConfig& config);
    void from_json(const nlohmann::json& j, HomeStandCharConfig& config);
    void to_json(nlohmann::json& j, const GlobalCharConfig& config);
    void from_json(const nlohmann::json& j, GlobalCharConfig& config);
    void to_json(nlohmann::json& j, const Config& config);
    void from_json(const nlohmann::json& j, Config& config);
}
