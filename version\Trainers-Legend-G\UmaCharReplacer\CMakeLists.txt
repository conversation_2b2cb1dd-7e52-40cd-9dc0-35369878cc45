cmake_minimum_required(VERSION 3.20)
project(UmaCharReplacer VERSION 1.0.0 LANGUAGES CXX ASM_MASM)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Platform and architecture settings
if(NOT CMAKE_SIZEOF_VOID_P EQUAL 8)
    message(FATAL_ERROR "This project requires 64-bit architecture")
endif()

# Compiler settings
if(MSVC)
    add_compile_options(/utf-8)
    add_compile_options(/std:c++latest)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(NOMINMAX)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
    
    # Release optimizations
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(/Os)  # Optimize for size
        add_link_options(/OPT:REF /OPT:ICF)
    endif()
    
    # Disable SAFESEH for compatibility
    add_link_options(/SAFESEH:NO)
endif()

# Force static linking
set(VCPKG_TARGET_TRIPLET "x64-windows-static")

# Find packages
find_package(minhook CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)

# Source files
set(SOURCES
    src/dllmain.cpp
    src/hook.cpp
    src/config.cpp
    src/character_replacer.cpp
    src/il2cpp_helper.cpp
    src/proxy.cpp
    src/version.asm
)

set(HEADERS
    src/config.hpp
    src/character_replacer.hpp
    src/il2cpp_helper.hpp
    src/common.hpp
)

# Create the main DLL
add_library(UmaCharReplacer SHARED ${SOURCES} ${HEADERS} src/version.def)

# Set target name to version.dll for proxy injection
set_target_properties(UmaCharReplacer PROPERTIES OUTPUT_NAME "version")

# Include directories
target_include_directories(UmaCharReplacer PRIVATE src)

# Link libraries
target_link_libraries(UmaCharReplacer PRIVATE
    minhook::minhook
    nlohmann_json::nlohmann_json
)

# Create hachimi directory and copy config file (for build output)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin/hachimi)
configure_file(${CMAKE_SOURCE_DIR}/ucrconfig.json ${CMAKE_BINARY_DIR}/bin/hachimi/ucrconfig.json COPYONLY)

# Installation
install(TARGETS UmaCharReplacer
    RUNTIME DESTINATION .
    LIBRARY DESTINATION .
)

install(FILES ucrconfig.json DESTINATION hachimi)

# Create a custom target for easy copying to game directory
add_custom_target(install_to_game
    COMMAND ${CMAKE_COMMAND} -E echo "Copy the following files to your Umamusume game directory:"
    COMMAND ${CMAKE_COMMAND} -E echo "1. ${CMAKE_BINARY_DIR}/bin/version.dll"
    COMMAND ${CMAKE_COMMAND} -E echo "2. ${CMAKE_BINARY_DIR}/bin/hachimi/ucrconfig.json"
    COMMAND ${CMAKE_COMMAND} -E echo "3. Rename original version.dll to version_orig.dll"
    DEPENDS UmaCharReplacer
)
